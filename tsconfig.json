{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "react-jsx",
    "lib": ["ESNext", "DOM"],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["./src/*"],
      "~/*": ["./*"]
    },
    "resolveJsonModule": true,
    "types": ["vite/client", "node", "unplugin-icons/types/react"],
    "allowImportingTsExtensions": true,
    /* Linting */
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": false,
    "noEmit": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true
  },
  "include": ["./**/*.ts", "./**/*.tsx"],
  "exclude": ["node_modules", "dist"]
}
