{"name": "soybean-admin-frontend", "type": "module", "version": "2.0.0", "private": false, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "description": "A fresh and elegant admin template, based on React19、Vite7、TypeScript、Ant Design and UnoCSS. 一个基于React19、Vite7、TypeScript、Ant Design and UnoCSS的清新优雅的中后台模版。", "author": {"name": "lechinyweb", "email": "<EMAIL>", "url": "https://github.com/lechinyweb"}, "license": "MIT", "homepage": "https://github.com/lechinyweb/soybean-admin", "repository": {"url": "https://github.com/lechinyweb/soybean-adminn.git"}, "bugs": {"url": "https://github.com/lechinyweb/soybean-admin/issues"}, "keywords": ["React admin", "react-admin-template", "Vite5", "TypeScript", "Ant Design", "antd-admin", "Redux", "React-Router V6", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "concurrently \"pnpm server:dev\" \"vite --mode test\" --names \"SERVER,CLIENT\" --prefix-colors \"blue,green\"", "dev:frontend": "vite --mode test", "dev:prod": "concurrently \"pnpm server:start\" \"vite --mode prod\" --names \"SERVER,CLIENT\" --prefix-colors \"blue,green\"", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "server:dev": "pnpm --filter soybean-admin-server dev", "server:init": "pnpm --filter soybean-admin-server init-db", "server:start": "pnpm --filter soybean-admin-server start", "setup-db": "node scripts/setup-database.js", "start": "pnpm build && pnpm server:start", "start:prod": "pnpm build --mode prod && pnpm server:start", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "1.0.3", "@better-scroll/core": "2.5.1", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@iconify/react": "6.0.0", "@reduxjs/toolkit": "2.8.2", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "ahooks": "3.9.0", "antd": "5.26.5", "clipboard": "2.0.11", "clsx": "2.1.1", "dayjs": "1.11.13", "echarts": "5.6.0", "i18next": "25.3.2", "keepalive-for-react": "4.0.2", "lodash-es": "4.17.21", "motion": "12.23.6", "nprogress": "0.2.0", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "6.0.0", "react-i18next": "15.6.0", "react-redux": "9.2.0", "react-router-dom": "7.7.0"}, "devDependencies": {"@iconify/json": "2.2.359", "@iconify/types": "2.0.0", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybean-react/vite-plugin-react-router": "0.7.1", "@soybeanjs/eslint-config": "1.7.1", "@svgr/core": "8.1.0", "@svgr/plugin-jsx": "8.1.0", "@types/express": "^5.0.3", "@types/gradient-string": "1.1.6", "@types/jsonwebtoken": "^9.0.10", "@types/lodash-es": "4.17.12", "@types/node": "24.0.14", "@types/nprogress": "0.2.3", "@types/pg": "^8.15.4", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-transition-group": "4.4.12", "@typescript-eslint/eslint-plugin": "8.37.0", "@typescript-eslint/parser": "8.37.0", "@unocss/eslint-config": "66.3.3", "@unocss/preset-icons": "66.3.3", "@unocss/preset-uno": "66.3.3", "@unocss/transformer-directives": "66.3.3", "@unocss/transformer-variant-group": "66.3.3", "@unocss/vite": "66.3.3", "boxen": "8.0.1", "concurrently": "^9.2.0", "consola": "3.4.2", "eslint": "9.31.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-sort": "4.0.0", "gradient-string": "3.0.0", "json5": "2.2.3", "kolorist": "1.8.0", "lint-staged": "16.1.2", "sass": "1.89.2", "simple-git-hooks": "2.13.0", "tsconfig-paths": "^4.2.0", "tsx": "4.20.3", "typescript": "5.8.3", "unplugin-auto-import": "19.3.0", "unplugin-icons": "22.1.0", "vite": "7.0.4", "vite-plugin-inspect": "11.3.0", "vite-plugin-remove-console": "2.2.0", "vite-plugin-svg-icons": "2.0.1"}, "pnpm": {"overrides": {"micromatch": "^4.0.8", "source-map-resolve": "^0.6.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.1", "urix": "^0.1.0", "stable": "^0.1.8", "svgo": "^3.3.2"}}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://react.soybeanjs.cn/home/"}