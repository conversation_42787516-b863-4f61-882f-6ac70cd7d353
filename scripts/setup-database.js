#!/usr/bin/env node

import { execSync } from 'node:child_process';
import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 开始设置数据库连接...\n');

// 获取项目根目录（脚本在 scripts 目录下，所以需要向上一级）
const projectRoot = path.dirname(__dirname);
const packageJsonPath = path.join(projectRoot, 'package.json');

if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ 请在项目根目录运行此脚本');
  process.exit(1);
}

// 检查 server 目录是否存在
const serverDir = path.join(projectRoot, 'server');
if (!fs.existsSync(serverDir)) {
  console.error('❌ server 目录不存在，请确保已创建后端服务');
  process.exit(1);
}

try {
  console.log('📦 安装后端依赖...');
  execSync('pnpm install', {
    cwd: serverDir,
    stdio: 'inherit'
  });

  console.log('\n🗄️ 初始化数据库...');
  execSync('pnpm run init-db', {
    cwd: serverDir,
    stdio: 'inherit'
  });

  console.log('\n✅ 数据库设置完成！');
  console.log('\n📋 接下来的步骤：');
  console.log('1. 启动前端和后端: pnpm dev');
  console.log('2. 或者分别启动:');
  console.log('   - 后端服务: pnpm server:dev');
  console.log('   - 前端服务: pnpm dev:frontend');
  console.log('3. 访问 http://localhost:9527 进行登录测试');
  console.log('\n👥 默认登录账号：');
  console.log('- 超级管理员: caspian / sa123456');
  console.log('- 管理员: admin / sa123456');
  console.log('- 普通用户: user / sa123456');
} catch (error) {
  console.error('❌ 设置过程中出现错误:', error.message);
  process.exit(1);
}
