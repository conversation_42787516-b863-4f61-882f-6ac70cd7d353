const theme: App.I18n.Schema['translation']['theme'] = {
  colourWeakness: 'Colour Weakness',
  configOperation: {
    copyConfig: 'Copy Config',
    copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
    resetConfig: 'Reset Config',
    resetSuccessMsg: 'Reset Success'
  },
  fixedHeaderAndTab: 'Fixed Header And Tab',
  footer: {
    fixed: 'Fixed Footer',
    height: 'Footer Height',
    right: 'Right Footer',
    visible: 'Footer Visible'
  },
  grayscale: 'Grayscale',
  header: {
    breadcrumb: {
      showIcon: 'Breadcrumb Icon Visible',
      visible: 'Breadcrumb Visible'
    },
    height: 'Header Height'
  },
  isOnlyExpandCurrentParentMenu: 'Only Expand Current Parent Menu',
  layoutMode: {
    horizontal: 'Horizontal Menu Mode',
    'horizontal-mix': 'Horizontal Mix menu Mode',
    reverseHorizontalMix: 'Reverse first level menus and child level menus position',
    title: 'Layout Mode',
    vertical: 'Vertical Menu Mode',
    'vertical-mix': 'Vertical Mix Menu Mode'
  },
  page: {
    animate: 'Page Animate',
    mode: {
      fade: 'Springing',
      'fade-bottom': 'Fade Zoom',
      'fade-scale': 'Fade Scale',
      'fade-slide': 'Slide',
      none: 'None',
      title: 'Page Animate Mode',
      'zoom-fade': 'Zoom Fade',
      'zoom-out': 'Zoom Out'
    }
  },
  pageFunTitle: 'Page Function',
  recommendColor: 'Apply Recommended Color Algorithm',
  recommendColorDesc: 'The recommended color algorithm refers to',
  scrollMode: {
    content: 'Content',
    title: 'Scroll Mode',
    wrapper: 'Wrapper'
  },
  sider: {
    collapsedWidth: 'Sider Collapsed Width',
    inverted: 'Dark Sider',
    mixChildMenuWidth: 'Mix Child Menu Width',
    mixCollapsedWidth: 'Mix Sider Collapse Width',
    mixWidth: 'Mix Sider Width',
    width: 'Sider Width'
  },
  tab: {
    cache: 'Tab Cache',
    height: 'Tab Height',
    mode: {
      button: 'Button',
      chrome: 'Chrome',
      title: 'Tab Mode'
    },
    visible: 'Tab Visible'
  },
  themeColor: {
    error: 'Error',
    followPrimary: 'Follow Primary',
    info: 'Info',
    primary: 'Primary',
    success: 'Success',
    title: 'Theme Color',
    warning: 'Warning'
  },
  themeDrawerTitle: 'Theme Configuration',
  themeSchema: {
    title: 'Theme Schema'
  },
  watermark: {
    text: 'Watermark Text',
    visible: 'Watermark Full Screen Visible'
  }
};

export default theme;
