const route: App.I18n.Schema['translation']['route'] = {
  '(base)_about': '关于',
  '(base)_data-manage': '数据管理',
  '(base)_data-manage_filing-data': '备案数据',
  '(base)_function': '系统功能',
  '(base)_function_event-bus': '事件总线演示',
  '(base)_function_hide-child': '隐藏子菜单',
  '(base)_function_hide-child_one': '隐藏子菜单',
  '(base)_function_hide-child_three': '菜单三',
  '(base)_function_hide-child_two': '菜单二',
  '(base)_function_multi-tab': '多标签页',
  '(base)_function_request': '请求',
  '(base)_function_super-page': '超级管理员可见',
  '(base)_function_tab': '标签页',
  '(base)_function_toggle-auth': '切换权限',
  '(base)_function_use-request': 'useRequest 演示',
  '(base)_home': '首页',
  '(base)_manage': '系统管理',
  '(base)_manage_role': '角色管理',
  '(base)_manage_role_[...slug]': '角色管理详情',
  '(base)_manage_user': '用户管理',
  '(base)_manage_user_[id]': '用户详情',
  '(base)_multi-menu': '多级菜单',
  '(base)_multi-menu_first': '菜单一',
  '(base)_multi-menu_first_child': '菜单一子菜单',
  '(base)_multi-menu_second': '菜单二',
  '(base)_multi-menu_second_child': '菜单二子菜单',
  '(base)_multi-menu_second_child_home': '菜单二子菜单首页',
  '(base)_projects': '多级动态路由',
  '(base)_projects_[pid]': '多级动态路由详情',
  '(base)_projects_[pid]_edit': '多级动态路由编辑',
  '(base)_projects_[pid]_edit_[id]': '多级动态路由编辑详情',
  '(base)_user-center': '个人中心',
  '(blank)_login': '登录',
  '(blank)_login_code-login': '验证码登录',
  '(blank)_login_register': ' 注册账号',
  '(blank)_login_reset-pwd': '重置密码',
  '(blank)_login-out': '退出登录',
  '403': '无权限',
  '404': '页面不存在',
  '500': '服务器错误',
  document: '文档',
  document_antd: 'Ant Design 文档',
  document_procomponents: 'ProComponents 文档',
  document_project: '项目文档',
  'document_project-link': '项目文档(外链)',
  document_react: 'React文档',
  document_ui: 'UI',
  document_unocss: 'UnoCSS文档',
  document_vite: 'Vite文档',
  exception: '异常页',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  'iframe-page': '外链页面',
  notFound: '页面不存在',
  root: '首页'
};

export default route;
