/* prettier-ignore */
/* eslint-disable */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router
// 请不要手动修改此文件，否则会导致优雅路由无法正常工作
// 如果需要修改，请在优雅路由配置文件中进行修改
// 这是自动生成的文件，请不要手动修改


import type { RouteKey, RouteMap, RoutePath } from '@soybean-react/vite-plugin-react-router';

/**
* map of route name and route path
*/
export const routeMap: RouteMap = {
 "not-found": "*",
 "exception": "/exception",
 "exception_403": "/exception/403",
 "exception_404": "/exception/404",
 "exception_500": "/exception/500",
 "document": "/document",
 "document_project": "/document/project",
 "document_project-link": "/document/project-link",
 "document_react": "/document/react",
 "document_vite": "/document/vite",
 "document_unocss": "/document/unocss",
 "document_procomponents": "/document/procomponents",
 "document_antd": "/document/antd",
 "document_ui": "/document/ui",
 "(base)_about": "/about",
 "(base)_data-manage": "/data-manage",
 "(base)_data-manage_filing-data": "/data-manage/filing-data",
 "(base)_function": "/function",
 "(base)_function_event-bus": "/function/event-bus",
 "(base)_function_hide-child": "/function/hide-child",
 "(base)_function_hide-child_one": "/function/hide-child/one",
 "(base)_function_hide-child_three": "/function/hide-child/three",
 "(base)_function_hide-child_two": "/function/hide-child/two",
 "(base)_function_multi-tab": "/function/multi-tab",
 "(base)_function_request": "/function/request",
 "(base)_function_super-page": "/function/super-page",
 "(base)_function_tab": "/function/tab",
 "(base)_function_toggle-auth": "/function/toggle-auth",
 "(base)_function_use-request": "/function/use-request",
 "(base)_home": "/home",
 "(base)_manage": "/manage",
 "(base)_manage_role": "/manage/role",
 "(base)_manage_role_[...slug]": "/manage/role/*",
 "(base)_manage_user": "/manage/user",
 "(base)_manage_user_[id]": "/manage/user/:id",
 "(base)_multi-menu": "/multi-menu",
 "(base)_multi-menu_first": "/multi-menu/first",
 "(base)_multi-menu_first_child": "/multi-menu/first/child",
 "(base)_multi-menu_second": "/multi-menu/second",
 "(base)_multi-menu_second_child": "/multi-menu/second/child",
 "(base)_multi-menu_second_child_home": "/multi-menu/second/child/home",
 "(base)_projects": "/projects",
 "(base)_projects_[pid]": "/projects/:pid",
 "(base)_projects_[pid]_edit": "/projects/:pid/edit",
 "(base)_projects_[pid]_edit_[id]": "/projects/:pid/edit/:id",
 "(base)_user-center": "/user-center",
 "(blank)_login": "/login",
 "(blank)_login_code-login": "/login/code-login",
 "(blank)_login_register": "/login/register",
 "(blank)_login_reset-pwd": "/login/reset-pwd",
 "(blank)_login-out": "/login-out",
 "403": "/403",
 "404": "/404",
 "500": "/500",
 "iframe-page": "/iframe-page",
 "root": "/"
};

/**
* get route path by route name
*
* @param name route name
*/
export function getRoutePath<T extends RouteKey>(name: T) {
 return routeMap[name];
}

/**
* get route name by route path
*
* @param path route path
*/
export function getRouteName(path: RoutePath) {
 const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

 const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

 return routeName;
}