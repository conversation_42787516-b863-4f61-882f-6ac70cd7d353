import { request } from '../request';

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    method: 'get',
    params,
    url: '/systemManage/getRoleList'
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.AllRole[]>({
    method: 'get',
    url: '/systemManage/getAllRoles'
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    method: 'get',
    params,
    url: '/systemManage/getUserList'
  });
}

/** get menu list */
export function fetchGetMenuList() {
  return request<Api.SystemManage.MenuList>({
    method: 'get',
    url: '/systemManage/getMenuList/v2'
  });
}

/** get all pages */
export function fetchGetAllPages() {
  return request<string[]>({
    method: 'get',
    url: '/systemManage/getAllPages'
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    method: 'get',
    url: '/systemManage/getMenuTree'
  });
}

/** add user */
export function fetchAddUser(data: Omit<Api.SystemManage.User, 'createTime' | 'id' | 'updateTime'>) {
  return request({
    data,
    method: 'post',
    url: '/systemManage/addUser'
  });
}

/** edit user */
export function fetchEditUser(id: number, data: Omit<Api.SystemManage.User, 'createTime' | 'id' | 'updateTime'>) {
  return request({
    data,
    method: 'put',
    url: `/systemManage/editUser/${id}`
  });
}

/** delete user */
export function fetchDeleteUser(id: number) {
  return request({
    method: 'delete',
    url: `/systemManage/deleteUser/${id}`
  });
}

/** batch delete users */
export function fetchBatchDeleteUsers(ids: number[]) {
  return request({
    data: { ids },
    method: 'delete',
    url: '/systemManage/batchDeleteUsers'
  });
}

/** delete role */
export function fetchDeleteRole(id: number) {
  return request({
    method: 'delete',
    url: `/systemManage/deleteRole/${id}`
  });
}

/** batch delete roles */
export function fetchBatchDeleteRoles(ids: number[]) {
  return request({
    data: { ids },
    method: 'delete',
    url: '/systemManage/batchDeleteRoles'
  });
}

/** update role menu permissions */
export function fetchUpdateRoleMenus(id: number, menuPermissions: string[], homePath?: string) {
  return request({
    data: { homePath, menuPermissions },
    method: 'put',
    url: `/systemManage/updateRoleMenus/${id}`
  });
}

/** get all buttons */
export function fetchGetAllButtons() {
  return request<{ key: string; title: string }[]>({
    method: 'get',
    url: '/systemManage/getAllButtons'
  });
}

/** update role button permissions */
export function fetchUpdateRoleButtons(id: number, buttonPermissions: string[]) {
  return request({
    data: { buttonPermissions },
    method: 'put',
    url: `/systemManage/updateRoleButtons/${id}`
  });
}
