import { request } from '../request';

/** 备案数据搜索参数 */
export interface FilingDataSearchParams {
  brand?: string;
  category?: string;
  current?: number;
  endDate?: string;
  manufacturer?: string;
  name?: string;
  province?: string;
  size?: number;
  startDate?: string;
}

/** 产品信息 */
export interface ProductInfo {
  allRecordEfficacy: string;
  brand: string;
  category: string;
  cosmeticId: string;
  cosmeticsType: string;
  createTime: string;
  id: number;
  launchDate: string;
  manufacturer: string;
  name: string;
  obmName: string;
  place: string;
  province: string;
}

/** 成分信息 */
export interface IngredientInfo {
  materialName: string;
  position: number;
}

/** 产品详情 */
export interface ProductDetail {
  ingredients: IngredientInfo[];
  product: ProductInfo;
}

/** 备案数据列表 */
export interface FilingDataList {
  current: number;
  records: ProductInfo[];
  size: number;
  total: number;
}

/** 统计信息 */
export interface FilingStats {
  brandCount: number;
  ingredientCount: number;
  manufacturerCount: number;
  productCount: number;
}

/** 首页统计信息 */
export interface HomeStats {
  brandCount: number;
  ingredientCount: number;
  manufacturerCount: number;
  productCount: number;
}

/** 每日备案数据 */
export interface DailyFilingData {
  count: number;
  date: string;
}

/** 月度对比数据 */
export interface MonthlyComparisonData {
  julyData: DailyFilingData[];
  juneData: DailyFilingData[];
}

/** 彩妆分类统计数据 */
export interface CosmeticCategoryData {
  name: string;
  value: number;
}

/** 获取备案数据列表 */
export function fetchFilingDataList(params?: FilingDataSearchParams) {
  return request<FilingDataList>({
    method: 'get',
    params,
    url: '/dataManage/getFilingDataList'
  });
}

/** 获取产品详情 */
export function fetchProductDetail(cosmeticId: string) {
  return request<ProductDetail>({
    method: 'get',
    url: `/dataManage/getProductDetail/${cosmeticId}`
  });
}

/** 获取统计信息 */
export function fetchFilingStats() {
  return request<FilingStats>({
    method: 'get',
    url: '/dataManage/getFilingStats'
  });
}

/** 获取首页统计信息 */
export function fetchHomeStats() {
  return request<HomeStats>({
    method: 'get',
    url: '/dataManage/getHomeStats'
  });
}

/** 获取月度对比数据 */
export function fetchMonthlyComparison() {
  return request<MonthlyComparisonData>({
    method: 'get',
    url: '/dataManage/getMonthlyComparison'
  });
}

/** 获取彩妆子分类统计数据 */
export function fetchCosmeticCategoryStats() {
  return request<CosmeticCategoryData[]>({
    method: 'get',
    url: '/dataManage/getCosmeticCategoryStats'
  });
}
