/* prettier-ignore */
/* eslint-disable */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router
// 请不要手动修改此文件，否则会导致优雅路由无法正常工作
// 如果需要修改，请在优雅路由配置文件中进行修改
// 这是自动生成的文件，请不要手动修改

import '@soybean-react/vite-plugin-react-router';

declare module "@soybean-react/vite-plugin-react-router" {

  /**
   * route map
   */
  export type RouteMap = {
    "not-found": "*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "document": "/document";
    "document_project": "/document/project";
    "document_project-link": "/document/project-link";
    "document_react": "/document/react";
    "document_vite": "/document/vite";
    "document_unocss": "/document/unocss";
    "document_procomponents": "/document/procomponents";
    "document_antd": "/document/antd";
    "document_ui": "/document/ui";
    "(base)_about": "/about";
    "(base)_data-manage": "/data-manage";
    "(base)_data-manage_filing-data": "/data-manage/filing-data";
    "(base)_function": "/function";
    "(base)_function_event-bus": "/function/event-bus";
    "(base)_function_hide-child": "/function/hide-child";
    "(base)_function_hide-child_one": "/function/hide-child/one";
    "(base)_function_hide-child_three": "/function/hide-child/three";
    "(base)_function_hide-child_two": "/function/hide-child/two";
    "(base)_function_multi-tab": "/function/multi-tab";
    "(base)_function_request": "/function/request";
    "(base)_function_super-page": "/function/super-page";
    "(base)_function_tab": "/function/tab";
    "(base)_function_toggle-auth": "/function/toggle-auth";
    "(base)_function_use-request": "/function/use-request";
    "(base)_home": "/home";
    "(base)_manage": "/manage";
    "(base)_manage_role": "/manage/role";
    "(base)_manage_role_[...slug]": "/manage/role/*";
    "(base)_manage_user": "/manage/user";
    "(base)_manage_user_[id]": "/manage/user/:id";
    "(base)_multi-menu": "/multi-menu";
    "(base)_multi-menu_first": "/multi-menu/first";
    "(base)_multi-menu_first_child": "/multi-menu/first/child";
    "(base)_multi-menu_second": "/multi-menu/second";
    "(base)_multi-menu_second_child": "/multi-menu/second/child";
    "(base)_multi-menu_second_child_home": "/multi-menu/second/child/home";
    "(base)_projects": "/projects";
    "(base)_projects_[pid]": "/projects/:pid";
    "(base)_projects_[pid]_edit": "/projects/:pid/edit";
    "(base)_projects_[pid]_edit_[id]": "/projects/:pid/edit/:id";
    "(base)_user-center": "/user-center";
    "(blank)_login": "/login";
    "(blank)_login_code-login": "/login/code-login";
    "(blank)_login_register": "/login/register";
    "(blank)_login_reset-pwd": "/login/reset-pwd";
    "(blank)_login-out": "/login-out";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "iframe-page": "/iframe-page";
    "root": "/";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document"
    | "document_project"
    | "document_project-link"
    | "document_react"
    | "document_vite"
    | "document_unocss"
    | "document_procomponents"
    | "document_antd"
    | "document_ui"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "(base)"
    | "(blank)"
    | "403"
    | "404"
    | "500"
    | "iframe-page"
    | "root"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "not-found"
    | "exception"
    | "document"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "(base)_about"
    | "(base)_data-manage_filing-data"
    | "(base)_data-manage"
    | "(base)_function_event-bus"
    | "(base)_function_hide-child"
    | "(base)_function_hide-child_one"
    | "(base)_function_hide-child_three"
    | "(base)_function_hide-child_two"
    | "(base)_function"
    | "(base)_function_multi-tab"
    | "(base)_function_request"
    | "(base)_function_super-page"
    | "(base)_function_tab"
    | "(base)_function_toggle-auth"
    | "(base)_function_use-request"
    | "(base)_home"
    | "(base)"
    | "(base)_manage"
    | "(base)_manage_role_[...slug]"
    | "(base)_manage_role"
    | "(base)_manage_user_[id]"
    | "(base)_manage_user"
    | "(base)_multi-menu_first_child"
    | "(base)_multi-menu_first"
    | "(base)_multi-menu"
    | "(base)_multi-menu_second_child_home"
    | "(base)_multi-menu_second_child"
    | "(base)_multi-menu_second"
    | "(base)_projects_[pid]_edit_[id]"
    | "(base)_projects_[pid]_edit"
    | "(base)_projects_[pid]"
    | "(base)_projects"
    | "(base)_user-center"
    | "(blank)"
    | "(blank)_login-out"
    | "(blank)_login_code-login"
    | "(blank)_login"
    | "(blank)_login"
    | "(blank)_login_register"
    | "(blank)_login_reset-pwd"
    | "403"
    | "404"
    | "500"
    | "iframe-page"
    | "root"
    | "root"
    | "root"
    | "root"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document_project"
    | "document_project-link"
    | "document_react"
    | "document_vite"
    | "document_unocss"
    | "document_procomponents"
    | "document_antd"
    | "document_ui"
  >;
}
