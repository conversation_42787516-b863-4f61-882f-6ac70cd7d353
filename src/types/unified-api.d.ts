/**
 * 统一的API类型定义
 * 所有字段使用 camelCase 命名规范
 */

declare namespace UnifiedApi {
  /** 通用分页参数 */
  interface PaginationParams {
    current: number;
    size: number;
  }

  /** 通用分页响应 */
  interface PaginationResponse<T> {
    current: number;
    records: T[];
    size: number;
    total: number;
  }

  /** 通用记录基础字段 */
  interface BaseRecord {
    createTime: string;
    id: number;
    updateTime: string;
  }

  /** 启用状态 */
  type EnableStatus = '1' | '2';

  /** 用户性别 */
  type UserGender = '1' | '2';

  /** 统一的角色定义 */
  type UserRole = 'R_ADMIN' | 'R_SUPER' | 'R_USER';

  /** 用户信息 */
  interface UserInfo extends BaseRecord {
    buttons: string[];
    email?: string;
    nickName?: string;
    roles: UserRole[];
    status?: EnableStatus;
    userGender?: UserGender;
    userId: string;
    userName: string;
    userPhone?: string;
  }

  /** 用户列表搜索参数 */
  interface UserSearchParams extends PaginationParams {
    nickName?: string;
    status?: EnableStatus;
    userEmail?: string;
    userGender?: UserGender;
    userName?: string;
    userPhone?: string;
  }

  /** 用户列表响应 */
  type UserListResponse = PaginationResponse<UserInfo>;

  /** 角色信息 */
  interface RoleInfo extends BaseRecord {
    homePath?: string;
    menuPermissions?: string[];
    roleCode: string;
    roleDesc?: string;
    roleName: string;
    status?: EnableStatus;
  }

  /** 角色列表搜索参数 */
  interface RoleSearchParams extends PaginationParams {
    roleCode?: string;
    roleName?: string;
    status?: EnableStatus;
  }

  /** 角色列表响应 */
  type RoleListResponse = PaginationResponse<RoleInfo>;

  /** 产品信息 */
  interface ProductInfo extends BaseRecord {
    allRecordEfficacy: string;
    brand: string;
    category: string;
    cosmeticId: string;
    cosmeticsType: string;
    launchDate: string;
    manufacturer: string;
    name: string;
    obmName: string;
    place: string;
    province: string;
  }

  /** 成分信息 */
  interface IngredientInfo {
    materialName: string;
    position: number;
  }

  /** 产品详情 */
  interface ProductDetail {
    ingredients: IngredientInfo[];
    product: ProductInfo;
  }

  /** 备案数据搜索参数 */
  interface FilingDataSearchParams extends PaginationParams {
    brand?: string;
    category?: string;
    endDate?: string;
    manufacturer?: string;
    name?: string;
    province?: string;
    startDate?: string;
  }

  /** 备案数据列表响应 */
  type FilingDataListResponse = PaginationResponse<ProductInfo>;

  /** 统计信息 */
  interface StatsInfo {
    brandCount: number;
    ingredientCount: number;
    manufacturerCount: number;
    productCount: number;
  }

  /** 每日备案数据 */
  interface DailyFilingData {
    count: number;
    date: string;
  }

  /** 月度对比数据 */
  interface MonthlyComparisonData {
    julyData: DailyFilingData[];
    juneData: DailyFilingData[];
  }

  /** 登录请求 */
  interface LoginRequest {
    password: string;
    userName: string;
  }

  /** 登录响应 */
  interface LoginResponse {
    refreshToken: string;
    token: string;
  }

  /** 用户信息响应 */
  interface UserInfoResponse {
    buttons: string[];
    roles: UserRole[];
    userId: string;
    userName: string;
  }

  /** 路由权限响应 */
  interface UserRoutesResponse {
    home: string;
    routes: string[];
  }

  /** 菜单树节点 */
  interface MenuTreeNode {
    children?: MenuTreeNode[];
    key: string;
    title: string;
  }

  /** 通用API响应格式 */
  interface ApiResponse<T = any> {
    code: string;
    data: T;
    message: string;
  }

  /** 创建用户请求 */
  interface CreateUserRequest {
    nickName?: string;
    password: string;
    status?: EnableStatus;
    userEmail?: string;
    userGender?: UserGender;
    userName: string;
    userPhone?: string;
    userRoles: UserRole[];
  }

  /** 更新用户请求 */
  interface UpdateUserRequest {
    nickName?: string;
    password?: string;
    status?: EnableStatus;
    userEmail?: string;
    userGender?: UserGender;
    userName: string;
    userPhone?: string;
    userRoles: UserRole[];
  }

  /** 创建角色请求 */
  interface CreateRoleRequest {
    roleCode: string;
    roleDesc?: string;
    roleName: string;
    status?: EnableStatus;
  }

  /** 更新角色请求 */
  interface UpdateRoleRequest {
    roleCode: string;
    roleDesc?: string;
    roleName: string;
    status?: EnableStatus;
  }

  /** 更新角色菜单权限请求 */
  interface UpdateRoleMenusRequest {
    homePath?: string;
    menuPermissions: string[];
  }
}
