import type { DataNode } from 'antd/es/tree';

import { fetchGetAllButtons, fetchGetRoleList, fetchUpdateRoleButtons } from '@/service/api';

import type { ModulesProps } from './type';

const ButtonAuthModal: FC<ModulesProps> = memo(({ onClose, open, roleId }) => {
  const title = '编辑按钮权限';

  const [checks, setChecks] = useState<string[]>();
  const [loading, setLoading] = useState(false);
  const [tree, setTree] = useState<DataNode[]>();

  async function getChecks() {
    if (!roleId) return;

    try {
      // 获取角色信息
      const { data: roleData } = await fetchGetRoleList({ current: 1, size: 100 });
      const role = roleData?.records?.find(r => r.id === roleId);

      if (role) {
        setChecks(role.buttonPermissions || []);
      }
    } catch {
      // Failed to get role button permissions
    }
  }

  async function getAllButtons() {
    try {
      const { data: buttonData } = await fetchGetAllButtons();
      const buttonTree =
        buttonData?.map(button => ({
          key: button.key,
          title: button.title
        })) || [];
      setTree(buttonTree);
    } catch {
      // Failed to get all buttons
    }
  }

  async function handleSubmit() {
    if (!roleId || !checks) return;

    setLoading(true);
    try {
      await fetchUpdateRoleButtons(roleId, checks);
      window.$message?.success?.('按钮权限更新成功');
      onClose();
    } catch {
      // Failed to update role button permissions
      window.$message?.error?.('按钮权限更新失败');
    } finally {
      setLoading(false);
    }
  }

  async function init() {
    await getAllButtons();
    await getChecks();
  }

  useUpdateEffect(() => {
    if (open) {
      init();
    }
  }, [open]);

  return (
    <AModal
      className="w-480px"
      open={open}
      title={title}
      footer={
        <ASpace className="mt-16px">
          <AButton
            size="small"
            onClick={onClose}
          >
            取消
          </AButton>
          <AButton
            loading={loading}
            size="small"
            type="primary"
            onClick={handleSubmit}
          >
            确认
          </AButton>
        </ASpace>
      }
      onCancel={onClose}
    >
      <ATree
        checkable
        checkedKeys={checks}
        className="h-280px"
        height={280}
        treeData={tree}
        onCheck={checkedKeys => {
          setChecks(Array.isArray(checkedKeys) ? (checkedKeys as string[]) : (checkedKeys.checked as string[]));
        }}
      />
    </AModal>
  );
});

export default ButtonAuthModal;
