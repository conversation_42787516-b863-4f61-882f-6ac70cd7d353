import {
  filterAndFlattenRoutes,
  flattenLeafRoutes,
  getBaseChildrenRoutes,
  getFlatBaseRoutes
} from '@/features/router/routes';
import { allRoutes } from '@/router';
import { fetchGetMenuTree, fetchGetRoleList, fetchUpdateRoleMenus } from '@/service/api';

import type { ModulesProps } from './type';

const flatRoutes = flattenLeafRoutes(getBaseChildrenRoutes(allRoutes));

const MenuAuthModal: FC<ModulesProps> = memo(({ onClose, open, roleId }) => {
  const { t } = useTranslation();

  const title = '编辑菜单权限';

  const [home, setHome] = useState<string>();

  const [checks, setChecks] = useState<string[]>();
  const [loading, setLoading] = useState(false);
  const [menuTree, setMenuTree] = useState<any[]>([]);

  const data = getFlatBaseRoutes(flatRoutes, t);

  const tree = filterAndFlattenRoutes(allRoutes[0].children || [], t);

  async function getChecks() {
    if (!roleId) return;

    try {
      // 获取角色信息
      const { data: roleData } = await fetchGetRoleList({ current: 1, size: 100 });
      const role = roleData?.records?.find(r => r.id === roleId);

      if (role) {
        setChecks(role.menuPermissions || []);
        setHome(role.homePath || '/home');
      }
    } catch {
      // Failed to get role menu permissions
    }
  }

  async function getMenuTree() {
    try {
      const { data: menuData } = await fetchGetMenuTree();
      setMenuTree(menuData || []);
    } catch {
      // Failed to get menu tree
    }
  }

  async function handleSubmit() {
    if (!roleId || !checks) return;

    setLoading(true);
    try {
      await fetchUpdateRoleMenus(roleId, checks, home);
      window.$message?.success?.('菜单权限更新成功');
      onClose();
    } catch {
      // Failed to update role menu permissions
      window.$message?.error?.('菜单权限更新失败');
    } finally {
      setLoading(false);
    }
  }

  async function init() {
    setHome('/home');
    await getChecks();
    await getMenuTree();
  }

  useUpdateEffect(() => {
    if (open) {
      init();
    }
  }, [open]);

  return (
    <AModal
      className="w-480px"
      open={open}
      title={title}
      footer={
        <ASpace className="mt-16px">
          <AButton
            size="small"
            onClick={onClose}
          >
            取消
          </AButton>
          <AButton
            loading={loading}
            size="small"
            type="primary"
            onClick={handleSubmit}
          >
            确认
          </AButton>
        </ASpace>
      }
      onCancel={onClose}
    >
      <div className="flex-y-center gap-16px pb-12px">
        <div>首页路径</div>

        <ASelect
          className="w-240px"
          options={data}
          value={home}
          onChange={setHome}
        />
      </div>

      <ATree
        checkable
        checkedKeys={checks}
        checkStrictly={false}
        className="h-280px"
        height={280}
        treeData={menuTree.length > 0 ? menuTree : tree}
        onCheck={checkedKeys => {
          setChecks(Array.isArray(checkedKeys) ? (checkedKeys as string[]) : (checkedKeys.checked as string[]));
        }}
      />
    </AModal>
  );
});

export default MenuAuthModal;
