import { useRequest } from '@sa/hooks';
import { Button, Checkbox, Drawer, Flex, Form, Input, Radio, Select } from 'antd';
import type { FC } from 'react';
import { useState } from 'react';

import { enableStatusOptions, userGenderOptions } from '@/constants/business';
import { useFormRules } from '@/features/form';
import { fetchGetAllRoles } from '@/service/api';

interface OptionsProps {
  label: string;
  value: string;
}

type Model = Pick<
  Api.SystemManage.User,
  'nickName' | 'status' | 'userEmail' | 'userGender' | 'userName' | 'userPhone' | 'userRoles'
> & {
  confirmPassword?: string;
  password?: string;
};

type RuleKey = Extract<keyof Model, 'status' | 'userName'> | 'password';

function getOptions(item: Api.SystemManage.AllRole) {
  return {
    label: item.roleName,
    value: item.roleCode
  };
}

const UserOperateDrawer: FC<Page.OperateDrawerProps> = ({ form, handleSubmit, onClose, open, operateType }) => {
  const { t } = useTranslation();
  const [changePassword, setChangePassword] = useState(false);

  const { data, run } = useRequest(fetchGetAllRoles, {
    manual: true
  });

  const { defaultRequiredRule } = useFormRules();

  const roleOptions: OptionsProps[] = data ? data.map(getOptions) : [];

  const getPasswordRule = () => {
    if (operateType === 'add') {
      return defaultRequiredRule;
    }
    return changePassword ? defaultRequiredRule : { required: false };
  };

  const rules: Record<RuleKey, App.Global.FormRule> = {
    password: getPasswordRule(),
    status: defaultRequiredRule,
    userName: defaultRequiredRule
  };

  useUpdateEffect(() => {
    if (open) {
      run();
      // 重置密码修改状态
      setChangePassword(false);
    }
  }, [open]);

  // 自定义提交处理，在提交前处理密码字段
  const customHandleSubmit = async () => {
    try {
      await form.validateFields();

      if (operateType === 'edit' && !changePassword) {
        // 如果是编辑模式且没有选择修改密码，将密码字段设置为空字符串
        form.setFieldValue('password', '');
      }

      // 调用原始的handleSubmit
      handleSubmit();
    } catch {
      // 表单验证失败，不需要处理
    }
  };

  return (
    <Drawer
      open={open}
      title={operateType === 'add' ? t('page.manage.user.addUser') : t('page.manage.user.editUser')}
      footer={
        <Flex justify="space-between">
          <Button onClick={onClose}>{t('common.cancel')}</Button>
          <Button
            type="primary"
            onClick={customHandleSubmit}
          >
            {t('common.confirm')}
          </Button>
        </Flex>
      }
      onClose={onClose}
    >
      <Form
        form={form}
        layout="vertical"
      >
        {operateType === 'edit' && (
          <Form.Item
            name="id"
            style={{ display: 'none' }}
          >
            <Input type="hidden" />
          </Form.Item>
        )}

        <Form.Item
          label={t('page.manage.user.userName')}
          name="userName"
          rules={[rules.userName]}
        >
          <Input placeholder={t('page.manage.user.form.userName')} />
        </Form.Item>

        {operateType === 'add' && (
          <Form.Item
            label="密码"
            name="password"
            rules={[rules.password]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
        )}

        {operateType === 'edit' && (
          <>
            <Form.Item>
              <Checkbox
                checked={changePassword}
                onChange={e => setChangePassword(e.target.checked)}
              >
                修改密码
              </Checkbox>
            </Form.Item>
            {changePassword && (
              <Form.Item
                label="新密码"
                name="password"
                rules={[rules.password]}
              >
                <Input.Password placeholder="请输入新密码" />
              </Form.Item>
            )}
          </>
        )}

        <Form.Item
          label={t('page.manage.user.userGender')}
          name="userGender"
        >
          <Radio.Group>
            {userGenderOptions.map(item => (
              <Radio
                key={item.value}
                value={item.value}
              >
                {t(item.label)}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label={t('page.manage.user.nickName')}
          name="nickName"
        >
          <Input placeholder={t('page.manage.user.form.nickName')} />
        </Form.Item>

        <Form.Item
          label={t('page.manage.user.userPhone')}
          name="userPhone"
        >
          <Input placeholder={t('page.manage.user.form.userPhone')} />
        </Form.Item>

        <Form.Item
          label={t('page.manage.user.userEmail')}
          name="userEmail"
        >
          <Input placeholder={t('page.manage.user.form.userEmail')} />
        </Form.Item>

        <Form.Item
          label={t('page.manage.user.userStatus')}
          name="status"
          rules={[rules.status]}
        >
          <Radio.Group>
            {enableStatusOptions.map(item => (
              <Radio
                key={item.value}
                value={item.value}
              >
                {t(item.label)}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label={t('page.manage.user.userRole')}
          name="userRoles"
        >
          <Select
            options={roleOptions}
            placeholder={t('page.manage.user.form.userRole')}
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default UserOperateDrawer;
