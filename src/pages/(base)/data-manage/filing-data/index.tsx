import { useState } from 'react';

import SvgIcon from '@/components/SvgIcon';
import { useTable, useTableScroll } from '@/features/table';
import { type ProductInfo, fetchFilingDataList, fetchProductDetail } from '@/service/api';

import FilingSearch from './modules/filing-search';
import ProductDetail from './modules/product-detail';

const FilingData = () => {
  const { t } = useTranslation();
  const { scrollConfig, tableWrapperRef } = useTableScroll();
  const isMobile = useMobile();

  // 详情弹窗状态
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [currentDetail, setCurrentDetail] = useState<any>(null);
  const [loadingDetail, setLoadingDetail] = useState(false);

  // 查看详情处理
  const handleViewDetail = async (record: ProductInfo) => {
    setLoadingDetail(true);
    setDetailModalOpen(true);

    try {
      const { data: detail } = await fetchProductDetail(record.cosmeticId);
      setCurrentDetail(detail);
    } catch {
      window.$message?.error(t('page.filingData.fetchDetailError'));
      setDetailModalOpen(false);
    } finally {
      setLoadingDetail(false);
    }
  };

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setDetailModalOpen(false);
    setCurrentDetail(null);
  };

  // 表格配置
  const { run, searchProps, tableProps } = useTable({
    apiFn: fetchFilingDataList,
    apiParams: {
      brand: undefined,
      category: undefined,
      current: 1,
      endDate: undefined,
      manufacturer: undefined,
      name: undefined,
      province: undefined,
      size: 10,
      startDate: undefined
    },
    columns: () => [
      {
        align: 'center',
        dataIndex: 'index',
        key: 'index',
        title: t('common.index'),
        width: 64
      },
      {
        dataIndex: 'brand',
        key: 'brand',
        render: (text: string) => (
          <ATag
            className="rounded-full"
            color="blue"
          >
            {text}
          </ATag>
        ),
        title: t('page.filingData.brand'),
        width: 120
      },
      {
        dataIndex: 'name',
        key: 'name',
        render: (text: string) => (
          <div
            className="max-w-250px truncate font-medium"
            title={text}
          >
            {text}
          </div>
        ),
        title: t('page.filingData.productName'),
        width: 250
      },
      {
        dataIndex: 'manufacturer',
        key: 'manufacturer',
        render: (text: string) => (
          <div
            className="max-w-200px truncate text-gray-600"
            title={text}
          >
            {text || '-'}
          </div>
        ),
        title: t('page.filingData.manufacturer'),
        width: 200
      },
      {
        dataIndex: 'category',
        key: 'category',
        render: (text: string) => (
          <ATag
            className="rounded-full"
            color="green"
          >
            {text}
          </ATag>
        ),
        title: t('page.filingData.category'),
        width: 140
      },
      {
        dataIndex: 'province',
        key: 'province',
        render: (text: string) => (
          <ATag
            className="rounded-full"
            color="orange"
          >
            {text}
          </ATag>
        ),
        title: t('page.filingData.province'),
        width: 100
      },
      {
        align: 'center',
        dataIndex: 'launchDate',
        key: 'launchDate',
        title: t('page.filingData.launchDate'),
        width: 120
      },
      {
        align: 'center',
        key: 'operate',
        render: (_, record: ProductInfo) => (
          <AButton
            size="small"
            type="link"
            onClick={() => handleViewDetail(record)}
          >
            {t('page.filingData.viewDetail')}
          </AButton>
        ),
        title: t('page.filingData.operation'),
        width: 100
      }
    ],
    rowKey: 'cosmeticId'
  });

  return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      <ACollapse
        bordered={false}
        className="card-wrapper"
        defaultActiveKey={isMobile ? undefined : '1'}
        items={[
          {
            children: <FilingSearch {...searchProps} />,
            key: '1',
            label: t('common.search')
          }
        ]}
      />

      <ACard
        className="flex-col-stretch card-wrapper sm:flex-1-hidden"
        ref={tableWrapperRef}
        title={t('page.filingData.title')}
        variant="borderless"
        extra={
          <div className="flex items-center gap-3">
            <AButton
              icon={<SvgIcon icon="carbon:refresh" />}
              loading={tableProps.loading}
              onClick={() => run()}
            >
              {t('common.refresh')}
            </AButton>
          </div>
        }
      >
        <ATable
          scroll={scrollConfig}
          size="small"
          {...tableProps}
        />
      </ACard>

      {/* 产品详情弹窗 */}
      <ProductDetail
        detail={currentDetail}
        open={detailModalOpen}
        onClose={handleCloseDetail}
      />

      {/* 加载详情时的遮罩 */}
      {loadingDetail && (
        <AModal
          closable={false}
          footer={null}
          open={loadingDetail}
          width={300}
        >
          <div className="flex items-center justify-center py-8">
            <ASpin size="large" />
            <span className="ml-3">{t('page.filingData.loadingDetail')}</span>
          </div>
        </AModal>
      )}
    </div>
  );
};

export default FilingData;
