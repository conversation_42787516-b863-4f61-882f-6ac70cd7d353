import type { FormInstance } from 'antd';
import { DatePicker } from 'antd';

interface Props {
  form: FormInstance;
  reset: () => void;
  search: () => void;
  searchParams: any;
  updateSearchParams: (params: any) => void;
}

const FilingSearch = ({ form, reset, search, searchParams, updateSearchParams }: Props) => {
  const { t } = useTranslation();

  // 自定义搜索处理函数
  const handleSearch = async () => {
    try {
      const formValues = await form.validateFields();
      const { dateRange, ...rest } = formValues;

      // 处理日期范围转换
      const transformedParams: any = { ...rest };
      if (dateRange && dateRange.length === 2) {
        transformedParams.startDate = dateRange[0]?.format('YYYY-MM-DD');
        transformedParams.endDate = dateRange[1]?.format('YYYY-MM-DD');
      }

      // 直接更新搜索参数并触发搜索
      updateSearchParams(transformedParams);
      search(); // Use the search prop
    } catch (error) {
      console.error('搜索参数验证失败:', error);
    }
  };

  return (
    <AForm
      form={form}
      initialValues={searchParams}
      labelCol={{
        md: 8,
        span: 6
      }}
    >
      <ARow
        wrap
        align="top"
        gutter={[16, 16]}
        justify="start"
      >
        <ACol
          lg={6}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('page.filingData.productName')}
            name="name"
          >
            <AInput
              allowClear
              placeholder={t('page.filingData.placeholderProductName')}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={6}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('page.filingData.brand')}
            name="brand"
          >
            <AInput
              allowClear
              placeholder={t('page.filingData.placeholderBrand')}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={6}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('page.filingData.manufacturer')}
            name="manufacturer"
          >
            <AInput
              allowClear
              placeholder={t('page.filingData.placeholderManufacturer')}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={6}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('page.filingData.category')}
            name="category"
          >
            <AInput
              allowClear
              placeholder={t('page.filingData.placeholderCategory')}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={6}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('page.filingData.province')}
            name="province"
          >
            <AInput
              allowClear
              placeholder={t('page.filingData.placeholderProvince')}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={10}
          md={12}
          span={24}
        >
          <AForm.Item
            className="m-0"
            label={t('common.dateRange')}
            name="dateRange"
          >
            <DatePicker.RangePicker
              allowClear
              className="w-full"
              placeholder={[t('common.startDate'), t('common.endDate')]}
            />
          </AForm.Item>
        </ACol>

        <ACol
          lg={8}
          md={24}
          span={24}
        >
          <AForm.Item className="m-0">
            <div className="flex justify-end gap-3">
              <AButton
                icon={<IconIcRoundRefresh />}
                onClick={reset}
              >
                {t('common.reset')}
              </AButton>
              <AButton
                ghost
                icon={<IconIcRoundSearch />}
                type="primary"
                onClick={handleSearch}
              >
                {t('common.search')}
              </AButton>
            </div>
          </AForm.Item>
        </ACol>
      </ARow>
    </AForm>
  );
};

export default FilingSearch;
