import type { ProductDetail as ProductDetailType } from '@/service/api';

interface Props {
  detail: ProductDetailType | null;
  onClose: () => void;
  open: boolean;
}

const ProductDetail = ({ detail, onClose, open }: Props) => {
  const { t } = useTranslation();

  if (!detail) return null;

  const { ingredients, product } = detail;

  return (
    <AModal
      centered
      footer={null}
      open={open}
      width={900}
      title={
        <div className="flex items-center gap-2">
          <span>📋 {t('page.filingData.productDetail')}</span>
          <ATag color="blue">{product.brand}</ATag>
        </div>
      }
      onCancel={onClose}
    >
      <div>
        <ARow gutter={[16, 16]}>
          {/* 产品基本信息 */}
          <ACol span={24}>
            <ACard
              className="shadow-sm"
              size="small"
              title={`📋 ${t('page.filingData.productInfo')}`}
            >
              <ARow gutter={[16, 12]}>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.productName')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.name}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.brand')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.brand}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.launchDate')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.launchDate}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.manufacturer')}:</div>
                  <div
                    className="truncate text-gray-900 font-medium dark:text-gray-100"
                    title={product.manufacturer}
                  >
                    {product.manufacturer || '-'}
                  </div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.producer')}:</div>
                  <div
                    className="truncate text-gray-900 font-medium dark:text-gray-100"
                    title={product.obmName}
                  >
                    {product.obmName || '-'}
                  </div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.origin')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.place}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.category')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.category}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.type')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.cosmeticsType}</div>
                </ACol>
                <ACol
                  lg={8}
                  sm={12}
                  xs={24}
                >
                  <div className="mb-1 text-sm text-gray-600">{t('page.filingData.province')}:</div>
                  <div className="text-gray-900 font-medium dark:text-gray-100">{product.province}</div>
                </ACol>
              </ARow>
            </ACard>
          </ACol>

          {/* 功效信息 */}
          {product.allRecordEfficacy && (
            <ACol span={24}>
              <ACard
                className="shadow-sm"
                size="small"
                title={`✨ ${t('page.filingData.efficacyInfo')}`}
              >
                <div className="flex flex-wrap gap-1">
                  {product.allRecordEfficacy.split(',').map((efficacy, index) => (
                    <ATag
                      className="rounded-full text-xs"
                      color="blue"
                      key={index}
                    >
                      {efficacy.trim()}
                    </ATag>
                  ))}
                </div>
              </ACard>
            </ACol>
          )}

          {/* 成分信息 */}
          <ACol span={24}>
            <ACard
              className="shadow-sm"
              extra={<div className="text-sm text-gray-500">{t('page.filingData.sortByOrder')}</div>}
              size="small"
              title={`🧪 ${t('page.filingData.ingredientInfo')} (${t('page.filingData.totalCount', { count: ingredients.length })})`}
            >
              <ARow gutter={[6, 6]}>
                {ingredients.map((ingredient, index) => (
                  <ACol
                    key={index}
                    lg={6}
                    md={8}
                    sm={12}
                    xs={24}
                  >
                    <div className="flex items-center gap-2 border border-gray-200 rounded-md p-2 transition-shadow dark:border-gray-600 hover:shadow-sm">
                      <div className="h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-xs text-blue-600 font-medium dark:bg-blue-900 dark:text-blue-300">
                        {ingredient.position}
                      </div>
                      <div
                        className="flex-1 truncate text-xs text-gray-900 dark:text-gray-100"
                        title={ingredient.materialName}
                      >
                        {ingredient.materialName}
                      </div>
                    </div>
                  </ACol>
                ))}
              </ARow>
            </ACard>
          </ACol>
        </ARow>
      </div>
    </AModal>
  );
};

export default ProductDetail;
