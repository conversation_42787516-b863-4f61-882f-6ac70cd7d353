import { useTranslation } from 'react-i18next';

import { useLang } from '@/features/lang';
import { fetchCosmeticCategoryStats } from '@/service/api/data-manage';

const PieChart = () => {
  const { t } = useTranslation();

  const { locale } = useLang();
  const dataLoaded = useRef(false);

  const { domRef, updateOptions } = useEcharts(
    () => ({
      legend: {
        bottom: '1%',
        itemStyle: {
          borderWidth: 0
        },
        left: 'center'
      },
      series: [
        {
          avoidLabelOverlap: false,
          center: ['50%', '40%'],
          color: ['#5da8ff', '#8e9dff', '#fedc69', '#26deca', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'],
          data: [] as { name: string; value: number }[],
          emphasis: {
            label: {
              fontSize: '12',
              show: true
            }
          },
          itemStyle: {
            borderColor: '#fff',
            borderRadius: 10,
            borderWidth: 1
          },
          label: {
            position: 'center',
            show: false
          },
          labelLine: {
            show: false
          },
          name: t('page.home.cosmeticCategoryStats'),
          radius: ['45%', '75%'],
          type: 'pie'
        }
      ],
      tooltip: {
        trigger: 'item'
      }
    }),
    {
      onRender: () => {
        // 图表首次渲染完成后加载数据
        if (!dataLoaded.current) {
          dataLoaded.current = true;
          init();
        }
      }
    }
  );

  // 分类名称映射
  const getCategoryTranslationKey = (categoryName: string): string => {
    const categoryMap: Record<string, string> = {
      口红: 'lipstick',
      甲油胶: 'nailGel',
      眉笔: 'eyebrow',
      眼影盘: 'eyeShadow',
      眼线液: 'eyeLiner',
      粉底液: 'foundation',
      粉饼: 'powder',
      腮红: 'blush',
      隔离乳: 'isolation'
    };

    return categoryMap[categoryName] || 'other';
  };

  async function loadCategoryData() {
    try {
      const { data } = await fetchCosmeticCategoryStats();

      if (data && data.length > 0) {
        // 转换API数据为国际化格式
        const translatedData = data.map(item => ({
          name: t(`page.home.cosmeticCategories.${getCategoryTranslationKey(item.name)}`),
          value: item.value
        }));

        updateOptions(opts => {
          opts.series[0].data = translatedData;
          return opts;
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load cosmetic category data:', error);
      // 使用fallback数据
      updateOptions(opts => {
        opts.series[0].data = [
          { name: t('page.home.cosmeticCategories.nailGel'), value: 4842 },
          { name: t('page.home.cosmeticCategories.lipstick'), value: 2626 },
          { name: t('page.home.cosmeticCategories.foundation'), value: 1309 },
          { name: t('page.home.cosmeticCategories.eyeShadow'), value: 425 },
          { name: t('page.home.cosmeticCategories.isolation'), value: 394 },
          { name: t('page.home.cosmeticCategories.eyebrow'), value: 384 },
          { name: t('page.home.cosmeticCategories.powder'), value: 343 },
          { name: t('page.home.cosmeticCategories.blush'), value: 303 }
        ];
        return opts;
      });
    }
  }

  function updateLocale() {
    // 语言切换时重新加载数据
    loadCategoryData();
  }

  async function init() {
    loadCategoryData();
  }

  // 移除useMount，改为在图表渲染完成后加载数据
  // useMount(() => {
  //   init();
  // });

  useUpdateEffect(() => {
    updateLocale();
  }, [locale]);
  return (
    <ACard
      className="card-wrapper"
      title={t('page.home.cosmeticCategoryStats')}
      variant="borderless"
    >
      <div
        className="h-360px overflow-hidden"
        ref={domRef}
      />
    </ACard>
  );
};

export default PieChart;
