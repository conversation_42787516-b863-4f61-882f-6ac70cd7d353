import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import avatar from '@/assets/imgs/avatar.png';
import { selectUserInfo } from '@/features/auth/authStore';
import { fetchWeatherData } from '@/service/api';
import { CACHE_DURATION, CACHE_KEYS, getCache, setCache } from '@/utils/cache';

interface StatisticData {
  id: number;
  title: string;
  value: string;
}

const HeaderBanner = () => {
  const { t } = useTranslation();
  const userInfo = useAppSelector(selectUserInfo);

  // 天气数据状态
  const [weatherData, setWeatherData] = useState<Api.Weather.WeatherInfo | null>(null);
  const [weatherLoading, setWeatherLoading] = useState(true);

  // 获取天气数据
  useEffect(() => {
    const getWeatherData = async () => {
      try {
        // 检查缓存
        const cachedWeatherData = getCache<Api.Weather.WeatherInfo>(CACHE_KEYS.WEATHER_DATA);
        if (cachedWeatherData) {
          setWeatherData(cachedWeatherData);
          setWeatherLoading(false);
          return;
        }

        setWeatherLoading(true);
        const response = await fetchWeatherData();
        // 检查响应结构 - 天气数据直接在 response.data 中
        if (response && response.data && response.data.location) {
          setWeatherData(response.data);
          // 缓存数据，10分钟有效期
          setCache(CACHE_KEYS.WEATHER_DATA, response.data, CACHE_DURATION.WEATHER);
        }
      } catch {
        // 如果请求失败，尝试使用缓存数据（即使过期）
        const cachedWeatherData = getCache<Api.Weather.WeatherInfo>(CACHE_KEYS.WEATHER_DATA);
        if (cachedWeatherData) {
          setWeatherData(cachedWeatherData);
        }
      } finally {
        setWeatherLoading(false);
      }
    };

    getWeatherData();
  }, []);

  // 根据时间获取问候语
  const getGreeting = () => {
    const hour = new Date().getHours();

    if (hour >= 5 && hour < 12) {
      return t('page.home.greetings.morning');
    } else if (hour >= 12 && hour < 14) {
      return t('page.home.greetings.noon');
    } else if (hour >= 14 && hour < 18) {
      return t('page.home.greetings.afternoon');
    } else if (hour >= 18 && hour < 22) {
      return t('page.home.greetings.evening');
    }
    return t('page.home.greetings.lateNight');
  };

  // 格式化天气描述
  const getWeatherDescription = () => {
    if (weatherLoading) {
      return t('page.home.weatherDesc'); // 加载中显示默认文本
    }

    if (weatherData) {
      return t('page.home.weatherFormat', {
        location: weatherData.location,
        temperature: weatherData.temperature,
        weather: weatherData.text
      });
    }

    return t('page.home.weatherDesc'); // 失败时显示默认文本
  };

  const statisticData: StatisticData[] = [
    {
      id: 0,
      title: t('page.home.projectCount'),
      value: '25'
    },
    {
      id: 1,
      title: t('page.home.todo'),
      value: '4/16'
    },
    {
      id: 2,
      title: t('page.home.message'),
      value: '12'
    }
  ];
  return (
    <ACard
      className="card-wrapper"
      variant="borderless"
    >
      <ARow gutter={[16, 16]}>
        <ACol
          md={18}
          span={24}
        >
          <div className="flex-y-center">
            <div className="size-72px shrink-0 overflow-hidden rd-1/2">
              <img
                className="size-full"
                src={avatar}
              />
            </div>
            <div className="pl-12px">
              <h3 className="text-18px font-semibold">
                {t('page.home.greeting', { greeting: getGreeting(), userName: userInfo.userName })}
              </h3>
              <p className="text-#999 leading-30px">{getWeatherDescription()}</p>
            </div>
          </div>
        </ACol>

        <ACol
          md={6}
          span={24}
        >
          <ASpace
            className="w-full justify-end"
            size={24}
          >
            {statisticData.map(item => (
              <AStatistic
                className="whitespace-nowrap"
                key={item.id}
                {...item}
              />
            ))}
          </ASpace>
        </ACol>
      </ARow>
    </ACard>
  );
};

export default HeaderBanner;
