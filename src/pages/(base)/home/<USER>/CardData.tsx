import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import NumberTicker from '@/components/NumberTicker';
import { type FilingStats, fetchFilingStats } from '@/service/api/data-manage';

interface CardDataProps {
  color: {
    end: string;
    start: string;
  };
  icon: string;
  key: string;
  title: string;
  unit: string;
  value: number;
}

function getGradientColor(color: CardDataProps['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}

function useGetCardData() {
  const { t } = useTranslation();
  const [stats, setStats] = useState<FilingStats>({
    brandCount: 0,
    ingredientCount: 0,
    manufacturerCount: 0,
    productCount: 0
  });
  const [loading, setLoading] = useState(true);

  // 获取统计数据
  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true);
        const { data } = await fetchFilingStats();
        if (data) {
          setStats(data);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to fetch filing stats:', error);
        // 使用默认数据作为fallback
        setStats({
          brandCount: 4515,
          ingredientCount: 3605,
          manufacturerCount: 3085,
          productCount: 15164
        });
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, []);

  const cardData: CardDataProps[] = [
    {
      color: {
        end: '#b955a4',
        start: '#ec4786'
      },
      icon: 'ant-design:skin-outlined',
      key: 'productCount',
      title: t('page.home.productCount'),
      unit: '',
      value: stats.productCount
    },
    {
      color: {
        end: '#5144b4',
        start: '#865ec0'
      },
      icon: 'ant-design:crown-outlined',
      key: 'brandCount',
      title: t('page.home.brandCount'),
      unit: '',
      value: stats.brandCount
    },
    {
      color: {
        end: '#719de3',
        start: '#56cdf3'
      },
      icon: 'ant-design:experiment-outlined',
      key: 'ingredientCount',
      title: t('page.home.ingredientCount'),
      unit: '',
      value: stats.ingredientCount
    },
    {
      color: {
        end: '#f68057',
        start: '#fcbc25'
      },
      icon: 'ant-design:shop-outlined',
      key: 'manufacturerCount',
      title: t('page.home.manufacturerCount'),
      unit: '',
      value: stats.manufacturerCount
    }
  ];

  return { cardData, loading };
}

const CardItem = (data: CardDataProps) => {
  return (
    <ACol
      key={data.key}
      lg={6}
      md={12}
      span={24}
    >
      <div
        className="flex-1 rd-8px px-16px pb-4px pt-8px text-white"
        style={{ backgroundImage: getGradientColor(data.color) }}
      >
        <h3 className="text-16px">{data.title}</h3>
        <div className="flex justify-between pt-12px">
          <SvgIcon
            className="text-32px"
            icon={data.icon}
          />
          <NumberTicker
            className="text-30px"
            prefix={data.unit}
            value={data.value}
          />
        </div>
      </div>
    </ACol>
  );
};

const CardData = () => {
  const { cardData, loading } = useGetCardData();

  return (
    <ACard
      className="card-wrapper"
      size="small"
      variant="borderless"
    >
      <ASpin spinning={loading}>
        <ARow gutter={[16, 16]}>{cardData.map(CardItem)}</ARow>
      </ASpin>
    </ACard>
  );
};

export default CardData;
