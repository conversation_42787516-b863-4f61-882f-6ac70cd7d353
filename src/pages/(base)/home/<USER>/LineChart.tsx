import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useLang } from '@/features/lang';
import { fetchMonthlyComparison } from '@/service/api/data-manage';

const LineChart = () => {
  const { t } = useTranslation();
  const { locale } = useLang();
  const [loading, setLoading] = useState(true);
  const dataLoaded = useRef(false);

  const { domRef, updateOptions } = useEcharts(
    () => ({
      grid: {
        bottom: '3%',
        containLabel: true,
        left: '3%',
        right: '4%'
      },
      legend: {
        data: [t('page.home.juneFilingData'), t('page.home.julyFilingData')]
      },
      series: [
        {
          areaStyle: {
            color: {
              colorStops: [
                {
                  color: '#8e9dff',
                  offset: 0.25
                },
                {
                  color: '#fff',
                  offset: 1
                }
              ],
              type: 'linear',
              x: 0,
              x2: 0,
              y: 0,
              y2: 1
            }
          },
          color: '#8e9dff',
          data: [] as number[],
          emphasis: {
            focus: 'series'
          },
          name: t('page.home.juneFilingData'),
          smooth: true,
          type: 'line'
        },
        {
          areaStyle: {
            color: {
              colorStops: [
                {
                  color: '#26deca',
                  offset: 0.25
                },
                {
                  color: '#fff',
                  offset: 1
                }
              ],
              type: 'linear',
              x: 0,
              x2: 0,
              y: 0,
              y2: 1
            }
          },
          color: '#26deca',
          data: [],
          emphasis: {
            focus: 'series'
          },
          name: t('page.home.julyFilingData'),
          smooth: true,
          type: 'line'
        }
      ],
      tooltip: {
        axisPointer: {
          label: {
            backgroundColor: '#6a7985'
          },
          type: 'cross'
        },
        trigger: 'axis'
      },
      xAxis: {
        boundaryGap: false,
        data: [] as string[],
        type: 'category'
      },
      yAxis: {
        min: 0,
        type: 'value'
      }
    }),
    {
      onRender: () => {
        // 图表首次渲染完成后加载数据
        if (!dataLoaded.current) {
          dataLoaded.current = true;
          init();
        }
      }
    }
  );

  async function loadMonthlyData() {
    try {
      setLoading(true);
      const { data } = await fetchMonthlyComparison();

      if (data) {
        // 根据服务器返回的数据确定日期范围
        const allDates = [...data.juneData, ...data.julyData].map(item => item.date);
        const uniqueDates = [...new Set(allDates)].sort();

        // 如果有数据，使用数据中的日期范围；否则使用当前日期
        let dateLabels: string[];
        if (uniqueDates.length > 0) {
          // 从服务器数据中提取日期标签（只取日部分，格式为 "01", "02" 等）
          const julyDates = data.julyData.map(item => {
            const date = item.date.slice(8); // 取 DD 部分
            return Number.parseInt(date, 10).toString(); // 去掉前导零，"01" -> "1"
          });
          dateLabels = julyDates;
        } else {
          // fallback：使用当前日期生成
          const now = new Date();
          const currentDay = now.getDate();

          dateLabels = Array.from({ length: currentDay }, (_, i) => {
            const day = i + 1;
            return day.toString(); // 直接使用数字，不加前导零
          });
        }

        // 创建数据映射，使用日期的天数部分作为key
        const juneDataMap = new Map(
          data.juneData.map(item => {
            const day = Number.parseInt(item.date.slice(8), 10).toString(); // 提取天数并去掉前导零
            return [day, item.count];
          })
        );
        const julyDataMap = new Map(
          data.julyData.map(item => {
            const day = Number.parseInt(item.date.slice(8), 10).toString(); // 提取天数并去掉前导零
            return [day, item.count];
          })
        );

        // 填充数据，没有数据的日期设为0
        const juneValues = dateLabels.map(day => juneDataMap.get(day) || 0);
        const julyValues = dateLabels.map(day => julyDataMap.get(day) || 0);

        updateOptions(opts => {
          opts.xAxis.data = dateLabels;
          opts.series[0].data = juneValues;
          opts.series[1].data = julyValues;
          return opts;
        });
      }
    } catch {
      // Failed to load monthly data, using fallback data
      // 使用模拟数据作为fallback
      const now = new Date();
      const currentDay = now.getDate();

      // 生成到当前日期的fallback数据（只显示天数）
      const fallbackDates = Array.from({ length: currentDay }, (_, i) => {
        const day = i + 1;
        return day.toString(); // 只返回天数，如 "1", "2", "3"
      });

      const fallbackJuneData = Array.from({ length: currentDay }, () => Math.floor(Math.random() * 300) + 200);

      const fallbackJulyData = Array.from({ length: currentDay }, () => Math.floor(Math.random() * 200) + 100);

      updateOptions(opts => {
        opts.xAxis.data = fallbackDates;
        opts.series[0].data = fallbackJuneData;
        opts.series[1].data = fallbackJulyData;
        return opts;
      });
    } finally {
      setLoading(false);
    }
  }

  function init() {
    loadMonthlyData();
  }

  function updateLocale() {
    updateOptions((opts, factory) => {
      const originOpts = factory();
      opts.legend.data = originOpts.legend.data;
      opts.series[0].name = originOpts.series[0].name;
      opts.series[1].name = originOpts.series[1].name;

      return opts;
    });
  }
  // init - 移除useMount，改为在图表渲染完成后加载数据
  // useMount(() => {
  //   init();
  // });

  useUpdateEffect(() => {
    updateLocale();
  }, [locale]);
  return (
    <ACard
      className="card-wrapper"
      title={t('page.home.cosmeticFilingTrend')}
      variant="borderless"
    >
      <ASpin spinning={loading}>
        <div
          className="h-360px overflow-hidden"
          ref={domRef}
        />
      </ASpin>
    </ACard>
  );
};

export default LineChart;
