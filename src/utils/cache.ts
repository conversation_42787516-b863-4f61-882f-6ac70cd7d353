/** 本地存储缓存工具 */

interface CacheItem<T> {
  data: T;
  expiry: number;
  timestamp: number;
}

/**
 * 设置缓存
 *
 * @param key 缓存键
 * @param data 缓存数据
 * @param duration 缓存持续时间（毫秒）
 */
export function setCache<T>(key: string, data: T, duration: number): void {
  const cacheItem: CacheItem<T> = {
    data,
    expiry: Date.now() + duration,
    timestamp: Date.now()
  };

  try {
    localStorage.setItem(key, JSON.stringify(cacheItem));
  } catch (error) {
    console.warn('Failed to set cache:', error);
  }
}

/**
 * 获取缓存
 *
 * @param key 缓存键
 * @returns 缓存数据或null
 */
export function getCache<T>(key: string): T | null {
  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const cacheItem: CacheItem<T> = JSON.parse(cached);
    const now = Date.now();

    // 检查是否过期
    if (now > cacheItem.expiry) {
      localStorage.removeItem(key);
      return null;
    }

    return cacheItem.data;
  } catch (error) {
    console.warn('Failed to get cache:', error);
    return null;
  }
}

/**
 * 检查缓存是否存在且有效
 *
 * @param key 缓存键
 * @returns 是否有效
 */
export function isCacheValid(key: string): boolean {
  return getCache(key) !== null;
}

/**
 * 清除指定缓存
 *
 * @param key 缓存键
 */
export function clearCache(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to clear cache:', error);
  }
}

/** 清除所有过期缓存 */
export function clearExpiredCache(): void {
  try {
    const keys = Object.keys(localStorage);
    const now = Date.now();

    keys.forEach(key => {
      try {
        const cached = localStorage.getItem(key);
        if (!cached) return;

        const cacheItem = JSON.parse(cached);
        // 检查是否是我们的缓存格式且已过期
        if (cacheItem.expiry && now > cacheItem.expiry) {
          localStorage.removeItem(key);
        }
      } catch {
        // 忽略非JSON格式的项目
      }
    });
  } catch (error) {
    console.warn('Failed to clear expired cache:', error);
  }
}

/** 缓存常量 */
export const CACHE_KEYS = {
  WEATHER_DATA: 'weather_data_cache'
} as const;

/** 缓存持续时间常量（毫秒） */
export const CACHE_DURATION = {
  // 30分钟
  LONG: 60 * 60 * 1000, // 5分钟
  MEDIUM: 30 * 60 * 1000, // 10分钟
  SHORT: 5 * 60 * 1000,
  WEATHER: 10 * 60 * 1000 // 1小时
} as const;
