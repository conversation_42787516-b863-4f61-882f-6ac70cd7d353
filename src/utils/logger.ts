/**
 * 前端日志工具
 * 在开发环境显示详细日志，生产环境可选择性禁用
 */

type LogLevel = 'debug' | 'error' | 'info' | 'warn';

interface LogConfig {
  enableInProduction: boolean;
  level: LogLevel;
  prefix: string;
}

class Logger {
  private config: LogConfig;

  constructor(config: Partial<LogConfig> = {}) {
    this.config = {
      enableInProduction: false,
      level: 'debug',
      prefix: '[App]',
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      error: 3,
      info: 1,
      warn: 2
    };

    // 生产环境检查
    if (import.meta.env.PROD && !this.config.enableInProduction) {
      return false;
    }

    return levels[level] >= levels[this.config.level];
  }

  private formatMessage(level: LogLevel, message: string, data?: any): any[] {
    const timestamp = new Date().toISOString();
    const prefix = `${this.config.prefix} [${level.toUpperCase()}] ${timestamp}`;

    if (data !== undefined) {
      return [prefix, message, data];
    }
    return [prefix, message];
  }

  debug(message: string, data?: any): void {
    if (this.shouldLog('debug')) {
      console.debug(...this.formatMessage('debug', message, data));
    }
  }

  info(message: string, data?: any): void {
    if (this.shouldLog('info')) {
      console.info(...this.formatMessage('info', message, data));
    }
  }

  warn(message: string, data?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(...this.formatMessage('warn', message, data));
    }
  }

  error(message: string, data?: any): void {
    if (this.shouldLog('error')) {
      console.error(...this.formatMessage('error', message, data));
    }
  }

  // 便捷方法
  api = {
    error: (url: string, error: any) => {
      this.error(`API Error: ${url}`, error);
    },
    request: (url: string, method: string, data?: any) => {
      this.debug(`API Request: ${method} ${url}`, data);
    },
    response: (url: string, status: number, data?: any) => {
      this.debug(`API Response: ${status} ${url}`, data);
    }
  };

  auth = {
    error: (message: string, data?: any) => {
      this.error(`Auth Error: ${message}`, data);
    },
    login: (username: string) => {
      this.info(`User login: ${username}`);
    },
    logout: () => {
      this.info('User logout');
    }
  };

  router = {
    error: (message: string, data?: any) => {
      this.error(`Router Error: ${message}`, data);
    },
    navigate: (from: string, to: string) => {
      this.debug(`Route change: ${from} -> ${to}`);
    }
  };
}

// 创建默认logger实例
export const logger = new Logger();

// 导出便捷方法
export const { api, auth, debug, error, info, router, warn } = logger;

// 导出Logger类供自定义使用
export { Logger };

// 默认导出
export default logger;
