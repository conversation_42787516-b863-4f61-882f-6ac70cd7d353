# 日志系统使用指南

本项目已经使用专业的日志库替代了 `console.log`，提供了更好的日志管理和调试体验。

## 后端日志系统 (<PERSON>)

### 配置文件
- 位置: `server/config/logger.js`
- 日志文件存储: `server/logs/`
  - `combined.log`: 所有级别的日志
  - `error.log`: 仅错误级别的日志

### 日志级别
- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `http`: HTTP请求日志
- `debug`: 调试信息

### 使用方法

#### 基本使用
```javascript
const { info, error, warn, debug } = require('./config/logger');

// 基本日志
info('服务器启动成功');
error('数据库连接失败');
warn('配置文件缺少某些参数');
debug('调试信息');
```

#### 结构化日志
```javascript
const { api, auth, db } = require('./config/logger');

// API相关日志
api.info('用户登录成功', { userId: 123, ip: '***********' });
api.error('API请求失败', { error: error.message, endpoint: '/api/users' });

// 认证相关日志
auth.info('用户登录', { username: 'admin' });
auth.error('登录失败', { username: 'admin', reason: '密码错误' });

// 数据库相关日志
db.info('数据库连接成功');
db.error('查询失败', { query: 'SELECT * FROM users', error: error.message });
```

### 环境配置
- 开发环境: 显示所有级别的日志
- 生产环境: 仅显示 `info` 级别及以上的日志

## 前端日志系统

### 配置文件
- 位置: `src/utils/logger.ts`

### 使用方法

#### 基本导入
```typescript
import { logger, info, error, warn, debug } from '@/utils/logger';

// 或者使用特定模块
import { api, auth, router } from '@/utils/logger';
```

#### 基本使用
```typescript
// 基本日志
info('组件加载完成');
error('网络请求失败');
warn('配置参数可能有误');
debug('调试信息');
```

#### API日志
```typescript
import { api } from '@/utils/logger';

// API请求日志
api.request('/api/users', 'GET', { page: 1 });
api.response('/api/users', 200, { users: [...] });
api.error('/api/users', new Error('网络错误'));
```

#### 认证日志
```typescript
import { auth } from '@/utils/logger';

auth.login('admin');
auth.logout();
auth.error('登录失败', { reason: '密码错误' });
```

#### 路由日志
```typescript
import { router } from '@/utils/logger';

router.navigate('/home', '/profile');
router.error('路由跳转失败', { from: '/home', to: '/profile' });
```

### 环境配置
- 开发环境: 显示所有日志
- 生产环境: 默认不显示日志（可配置）

## 日志文件管理

### 日志轮转
Winston 支持日志轮转，可以配置：
- 按文件大小轮转
- 按时间轮转
- 保留历史日志数量

### 日志分析
可以使用以下工具分析日志：
- `grep`: 搜索特定日志
- `tail -f`: 实时查看日志
- ELK Stack: 企业级日志分析

### 示例命令
```bash
# 查看最新日志
tail -f server/logs/combined.log

# 搜索错误日志
grep "error" server/logs/combined.log

# 查看特定时间的日志
grep "2025-07-17 10:" server/logs/combined.log
```

## 最佳实践

### 1. 日志级别选择
- `error`: 系统错误、异常情况
- `warn`: 潜在问题、配置警告
- `info`: 重要的业务事件
- `debug`: 详细的调试信息

### 2. 结构化日志
```javascript
// 好的做法
logger.error('用户登录失败', {
  username: 'admin',
  ip: '***********',
  reason: '密码错误',
  timestamp: new Date().toISOString()
});

// 避免的做法
logger.error('用户 admin 从 *********** 登录失败，原因：密码错误');
```

### 3. 敏感信息处理
```javascript
// 避免记录敏感信息
logger.info('用户登录', {
  username: 'admin',
  // password: 'secret123', // 不要记录密码
  ip: '***********'
});
```

### 4. 性能考虑
- 在生产环境中适当调整日志级别
- 避免在循环中大量输出日志
- 使用异步日志写入

## 配置自定义

### 修改日志级别
```javascript
// 在 server/config/logger.js 中修改
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info', // 通过环境变量控制
  // ...
});
```

### 添加新的传输器
```javascript
// 添加邮件通知
transports.push(
  new winston.transports.Mail({
    to: '<EMAIL>',
    subject: 'Application Error',
    level: 'error'
  })
);
```

这个日志系统为项目提供了专业级的日志管理能力，有助于更好的调试、监控和维护应用程序。
