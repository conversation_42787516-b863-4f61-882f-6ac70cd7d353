{"name": "soybean-server", "version": "1.0.0", "private": true, "description": "Backend server for Soybean Admin with PostgreSQL", "author": "lechinyweb", "license": "MIT", "keywords": ["express", "postgresql", "admin", "backend"], "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon --exec \"npm run build && node dist/index.js\" --ext ts --watch src", "init-db": "node scripts/init-db.js", "start": "npm run build && node dist/index.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10", "typescript": "^5.8.3"}}