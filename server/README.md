# Soybean Admin Backend Server

基于 Express.js 和 PostgreSQL 的后端服务，为 Soybean Admin 前端提供 API 支持。

## 🚀 快速开始

### 1. 安装依赖

```bash
cd server
pnpm install
```

### 2. 配置环境变量

环境变量配置文件位于项目根目录的 `.env` 文件中。复制 `.env.example` 并根据你的配置进行修改：

```bash
cd ..  # 回到项目根目录
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和服务器参数：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hzpdata
DB_USER=caspian
DB_PASSWORD=sa123456

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=development
```

**注意**: 服务器会自动从项目根目录读取 `.env` 文件。

### 3. 初始化数据库

确保 PostgreSQL 服务正在运行，并且已创建 `hzpdata` 数据库，然后运行：

```bash
pnpm run init-db
```

这将创建用户表并插入默认用户数据。

### 4. 启动服务器

```bash
# 开发模式（自动重启）
pnpm run dev

# 生产模式
pnpm start
```

服务器将在 http://localhost:3001 启动。

## 📊 API 接口

### 认证相关

- `POST /auth/login` - 用户登录
- `GET /auth/getUserInfo` - 获取用户信息（需要认证）
- `POST /auth/refreshToken` - 刷新访问令牌

### 健康检查

- `GET /health` - 服务器健康状态

## 👥 默认用户

初始化后会创建以下默认用户：

- **超级管理员**: `caspian` / `sa123456`
- **管理员**: `admin` / `sa123456`
- **普通用户**: `user` / `sa123456`

## 🗄️ 数据库表结构

### meiye_users 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | SERIAL | 主键 |
| username | VARCHAR(50) | 用户名（唯一） |
| password | VARCHAR(255) | 密码（加密） |
| email | VARCHAR(100) | 邮箱 |
| roles | TEXT[] | 角色数组 |
| buttons | TEXT[] | 按钮权限数组 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 🔧 开发说明

- 使用 bcryptjs 进行密码加密
- 使用 JWT 进行身份认证
- 支持 CORS 跨域请求
- 包含请求日志和错误处理
