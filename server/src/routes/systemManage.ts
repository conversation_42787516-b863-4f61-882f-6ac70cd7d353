import { Router } from 'express';

import { SystemManageController } from '../controllers/SystemManageController.js';
import { authenticateToken } from '../middleware/index.js';

/**
 * 系统管理路由
 * 保持与原有路由完全一致
 */
const router = Router();
const systemManageController = new SystemManageController();

// 用户管理路由
router.get('/getUserList', authenticateToken, systemManageController.getUserList);
router.post('/addUser', authenticateToken, systemManageController.addUser);
router.put('/editUser/:id', authenticateToken, systemManageController.editUser);
router.delete('/deleteUser/:id', authenticateToken, systemManageController.deleteUser);
router.delete('/batchDeleteUsers', authenticateToken, systemManageController.batchDeleteUsers);

// 角色管理路由
router.get('/getRoleList', authenticateToken, systemManageController.getRoleList);
router.get('/getAllRoles', authenticateToken, systemManageController.getAllRoles);
router.delete('/deleteRole/:id', authenticateToken, systemManageController.deleteRole);
router.delete('/batchDeleteRoles', authenticateToken, systemManageController.batchDeleteRoles);
router.put('/updateRoleMenus/:id', authenticateToken, systemManageController.updateRoleMenus);
router.put('/updateRoleButtons/:id', authenticateToken, systemManageController.updateRoleButtons);

// 菜单管理路由
router.get('/getMenuTree', authenticateToken, SystemManageController.getMenuTree);
router.get('/getMenuList/v2', authenticateToken, SystemManageController.getMenuListV2);
router.get('/getAllPages', authenticateToken, SystemManageController.getAllPages);
router.get('/getAllButtons', authenticateToken, SystemManageController.getAllButtons);

export default router;
