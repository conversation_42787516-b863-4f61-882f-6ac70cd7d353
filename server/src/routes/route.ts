import { Router } from 'express';

import { AuthController } from '../controllers/AuthController.js';
import { authenticateToken } from '../middleware/index.js';

/**
 * 路由相关接口
 * 为了兼容前端的 API 调用路径
 */
const router = Router();

// 获取用户路由接口 - 兼容前端调用
router.get('/getReactUserRoutes', authenticateToken, AuthController.getUserRoute);

// 获取常量路由接口 - 暂时返回空数组
router.get('/getConstantRoutes', (_req, res) => {
  res.json({
    code: '0000',
    data: [],
    message: 'Success'
  });
});

// 检查路由是否存在接口 - 暂时返回 true
router.get('/isRouteExist', (_req, res) => {
  res.json({
    code: '0000',
    data: true,
    message: 'Success'
  });
});

export default router;
