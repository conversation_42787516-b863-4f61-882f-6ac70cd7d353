import { Router } from 'express';

import { AuthController } from '../controllers/AuthController.js';
import { authenticateToken } from '../middleware/index.js';

/**
 * 认证路由
 * 保持与原有路由完全一致
 */
const router = Router();
const authController = new AuthController();

// 登录接口
router.post('/login', authController.login);

// 获取用户信息接口
router.get('/getUserInfo', authenticateToken, AuthController.getUserInfo);

// 刷新token接口
router.post('/refreshToken', authController.refreshToken);

// 获取用户路由接口
router.get('/getUserRoute', authenticateToken, AuthController.getUserRoute);

export default router;
