/**
 * 请求验证工具函数
 * 保持与原有验证逻辑完全一致
 */

import type { NextFunction, Request, Response } from 'express';

import type { StrictObject } from '../types/index.js';

import { responseHelper } from './response';

/**
 * 验证规则类型
 */
interface ValidationRule {
  readonly custom?: (value: unknown) => boolean;
  readonly enum?: readonly unknown[];
  readonly max?: number;
  readonly min?: number;
  readonly pattern?: RegExp;
  readonly required?: boolean;
  readonly type?: 'array' | 'boolean' | 'number' | 'object' | 'string';
}

/**
 * 验证规则集合
 */
type ValidationRules = Record<string, ValidationRule>;

/**
 * 验证单个字段
 */
function validateField(value: unknown, rule: ValidationRule, fieldName: string): string | null {
  // 必填验证
  if (rule.required && (value === undefined || value === null || value === '')) {
    return `${fieldName} is required`;
  }

  // 如果不是必填且值为空，跳过其他验证
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return null;
  }

  // 类型验证
  if (rule.type) {
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    if (actualType !== rule.type) {
      return `${fieldName} must be of type ${rule.type}`;
    }
  }

  // 字符串长度验证
  if (rule.type === 'string' && typeof value === 'string') {
    if (rule.min !== undefined && value.length < rule.min) {
      return `${fieldName} must be at least ${rule.min} characters long`;
    }
    if (rule.max !== undefined && value.length > rule.max) {
      return `${fieldName} must be at most ${rule.max} characters long`;
    }
  }

  // 数字范围验证
  if (rule.type === 'number' && typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return `${fieldName} must be at least ${rule.min}`;
    }
    if (rule.max !== undefined && value > rule.max) {
      return `${fieldName} must be at most ${rule.max}`;
    }
  }

  // 数组长度验证
  if (rule.type === 'array' && Array.isArray(value)) {
    if (rule.min !== undefined && value.length < rule.min) {
      return `${fieldName} must have at least ${rule.min} items`;
    }
    if (rule.max !== undefined && value.length > rule.max) {
      return `${fieldName} must have at most ${rule.max} items`;
    }
  }

  // 正则表达式验证
  if (rule.pattern && typeof value === 'string') {
    if (!rule.pattern.test(value)) {
      return `${fieldName} format is invalid`;
    }
  }

  // 枚举值验证
  if (rule.enum && !rule.enum.includes(value)) {
    return `${fieldName} must be one of: ${rule.enum.join(', ')}`;
  }

  // 自定义验证
  if (rule.custom && !rule.custom(value)) {
    return `${fieldName} is invalid`;
  }

  return null;
}

/**
 * 验证请求数据
 */
function validate(data: StrictObject, rules: ValidationRules): string[] {
  const errors: string[] = [];

  for (const [fieldName, rule] of Object.entries(rules)) {
    const error = validateField(data[fieldName], rule, fieldName);
    if (error) {
      errors.push(error);
    }
  }

  return errors;
}

/**
 * 创建验证中间件
 */
export function validateRequest(rules: ValidationRules, source: 'body' | 'params' | 'query' = 'body') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const data = req[source] as StrictObject;
    const errors = validate(data, rules);

    if (errors.length > 0) {
      responseHelper.badRequest(res, errors.join('; '));
      return;
    }

    next();
  };
}

/**
 * 常用验证规则
 */
export const commonRules = {
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    type: 'string' as const
  },
  gender: {
    enum: [1, 2],
    type: 'number' as const
  },
  id: {
    min: 1,
    required: true,
    type: 'number' as const
  },
  pagination: {
    current: {
      min: 1,
      required: true,
      type: 'number' as const
    },
    size: {
      max: 100,
      min: 1,
      required: true,
      type: 'number' as const
    }
  },
  password: {
    max: 100,
    min: 6,
    required: true,
    type: 'string' as const
  },
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    type: 'string' as const
  },
  status: {
    enum: [1, 2],
    type: 'number' as const
  },
  username: {
    max: 50,
    min: 3,
    pattern: /^[a-zA-Z0-9_]+$/,
    required: true,
    type: 'string' as const
  }
} as const;
