/**
 * API 响应工具函数
 * 统一 API 响应格式，保持与原有格式完全一致
 */

import type { Response } from 'express';

import type { ApiResponse } from '../types/index.js';
import { HttpStatus } from '../types/index.js';

/**
 * 响应辅助工具类
 */
class ResponseHelper {
  /**
   * 成功响应
   */
  success<T>(res: Response, data: T, message = 'success'): void {
    const response: ApiResponse<T> = {
      code: '0000',
      data,
      message
    };
    res.status(200).json(response);
  }

  /**
   * 错误响应
   */
  error(res: Response, code: string, message: string, statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR): void {
    const response: ApiResponse<null> = {
      code,
      data: null,
      message
    };
    res.status(statusCode).json(response);
  }

  /**
   * 参数错误响应
   */
  badRequest(res: Response, message = 'Invalid parameters'): void {
    this.error(res, '400', message, 400);
  }

  /**
   * 未授权响应
   */
  unauthorized(res: Response, message = 'Unauthorized'): void {
    this.error(res, '401', message, 401);
  }

  /**
   * 禁止访问响应
   */
  forbidden(res: Response, message = 'Forbidden'): void {
    this.error(res, '403', message, 403);
  }

  /**
   * 资源不存在响应
   */
  notFound(res: Response, message = 'Resource not found'): void {
    this.error(res, '404', message, 404);
  }

  /**
   * 冲突响应
   */
  conflict(res: Response, message = 'Resource conflict'): void {
    this.error(res, '409', message, 409);
  }

  /**
   * 服务器内部错误响应
   */
  internalError(res: Response, message = 'Internal server error'): void {
    this.error(res, '500', message, 500);
  }

  /**
   * 分页响应
   */
  paginated<T>(
    res: Response,
    records: readonly T[],
    total: number,
    current: number,
    size: number,
    message = 'success'
  ): void {
    const data = {
      current,
      records,
      size,
      total
    };
    this.success(res, data, message);
  }
}

export const responseHelper = new ResponseHelper();
