/**
 * 字段映射工具函数
 * 用于统一前后端字段命名，避免来回映射
 * 保持与原有逻辑完全一致
 */

import type { StrictObject } from '@/types';

/**
 * 将数据库的 snake_case 字段转换为前端的 camelCase 字段
 */
export function snakeToCamel<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => snakeToCamel(item)) as T;
  }

  if (typeof obj !== 'object') {
    return obj;
  }

  const result: StrictObject = {};

  for (const [key, value] of Object.entries(obj as StrictObject)) {
    // 将 snake_case 转换为 camelCase
    const camelKey = key.replace(/_([a-z])/g, (_match, letter: string) => letter.toUpperCase());

    // 递归处理嵌套对象
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[camelKey] = snakeToCamel(value);
    } else if (Array.isArray(value)) {
      result[camelKey] = value.map(item => (typeof item === 'object' ? snakeToCamel(item) : item));
    } else {
      result[camelKey] = value;
    }
  }

  return result as T;
}

/**
 * 将前端的 camelCase 字段转换为数据库的 snake_case 字段
 */
export function camelToSnake<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => camelToSnake(item)) as T;
  }

  if (typeof obj !== 'object') {
    return obj;
  }

  const result: StrictObject = {};

  for (const [key, value] of Object.entries(obj as StrictObject)) {
    // 将 camelCase 转换为 snake_case
    const snakeKey = key.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`);

    // 递归处理嵌套对象
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[snakeKey] = camelToSnake(value);
    } else if (Array.isArray(value)) {
      result[snakeKey] = value.map(item => (typeof item === 'object' ? camelToSnake(item) : item));
    } else {
      result[snakeKey] = value;
    }
  }

  return result as T;
}

/**
 * 统一角色字段映射，保持原有映射关系
 */
export const ROLE_MAPPING = {
  // 数据库 -> 前端
  DB_TO_FRONTEND: {
    admin: 'R_ADMIN',
    super: 'R_SUPER',
    user: 'R_USER'
  } as const,
  // 前端 -> 数据库
  FRONTEND_TO_DB: {
    R_ADMIN: 'admin',
    R_SUPER: 'super',
    R_USER: 'user'
  } as const
} as const;

/**
 * 转换角色字段：数据库格式 -> 前端格式
 */
export function mapRolesToFrontend(dbRoles: readonly string[]): readonly string[] {
  if (!Array.isArray(dbRoles)) {
    return [];
  }

  return dbRoles.map(role => ROLE_MAPPING.DB_TO_FRONTEND[role as keyof typeof ROLE_MAPPING.DB_TO_FRONTEND] || 'R_USER');
}

/**
 * 转换角色字段：前端格式 -> 数据库格式
 */
export function mapRolesToDatabase(frontendRoles: readonly string[]): readonly string[] {
  if (!Array.isArray(frontendRoles)) {
    return ['user'];
  }

  return frontendRoles.map(
    role => ROLE_MAPPING.FRONTEND_TO_DB[role as keyof typeof ROLE_MAPPING.FRONTEND_TO_DB] || 'user'
  );
}

/**
 * 转换用户信息：数据库格式 -> 前端格式
 */
export function mapUserToFrontend(dbUser: StrictObject | null): StrictObject | null {
  if (!dbUser) return null;

  const camelUser = snakeToCamel(dbUser);

  // 特殊处理角色字段
  if (camelUser.roles && Array.isArray(camelUser.roles)) {
    camelUser.roles = mapRolesToFrontend(camelUser.roles);
  }

  // 统一字段名映射，保持原有映射关系
  const fieldMapping = {
    createdAt: 'createTime',
    updatedAt: 'updateTime',
    username: 'userName'
  } as const;

  const result = { ...camelUser };

  for (const [dbField, frontendField] of Object.entries(fieldMapping)) {
    if (result[dbField] !== undefined) {
      result[frontendField] = result[dbField];
      if (dbField !== frontendField) {
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete result[dbField];
      }
    }
  }

  // 确保数字类型的字段转换为字符串类型，与前端类型定义保持一致
  if (result.status !== undefined && result.status !== null) {
    result.status = String(result.status) as '1' | '2';
  }
  if (result.userGender !== undefined && result.userGender !== null) {
    result.userGender = String(result.userGender) as '1' | '2';
  }

  return result;
}

/**
 * 转换用户信息：前端格式 -> 数据库格式
 */
export function mapUserToDatabase(frontendUser: StrictObject | null): StrictObject | null {
  if (!frontendUser) return null;

  // 字段名映射，保持原有映射关系
  const fieldMapping = {
    userName: 'username'
  } as const;

  const mapped = { ...frontendUser };

  for (const [frontendField, dbField] of Object.entries(fieldMapping)) {
    if (mapped[frontendField] !== undefined) {
      mapped[dbField] = mapped[frontendField];
      if (frontendField !== dbField) {
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete mapped[frontendField];
      }
    }
  }

  // 特殊处理角色字段
  if (mapped.roles && Array.isArray(mapped.roles)) {
    mapped.roles = mapRolesToDatabase(mapped.roles);
  }

  return camelToSnake(mapped);
}

/**
 * 数据类型枚举
 */
type DataType = 'common' | 'ingredient' | 'product' | 'role' | 'user';

/**
 * 转换角色信息：数据库格式 -> 前端格式
 */
export function mapRoleToFrontend(dbRole: StrictObject | null): StrictObject | null {
  if (!dbRole) return null;

  const camelRole = snakeToCamel(dbRole);
  const result = { ...camelRole };

  // 确保数字类型的字段转换为字符串类型，与前端类型定义保持一致
  if (result.status !== undefined && result.status !== null) {
    result.status = String(result.status) as '1' | '2';
  }

  return result;
}

/**
 * 通用的API响应格式化函数
 */
export function formatApiResponse<T>(data: T, type: DataType = 'common'): T {
  if (!data) return data;

  switch (type) {
    case 'user':
      return (Array.isArray(data) ? data.map(mapUserToFrontend) : mapUserToFrontend(data as StrictObject)) as T;

    case 'role':
      return (Array.isArray(data) ? data.map(mapRoleToFrontend) : mapRoleToFrontend(data as StrictObject)) as T;

    case 'product':
    case 'ingredient':
    case 'common':
    default:
      return snakeToCamel(data);
  }
}
