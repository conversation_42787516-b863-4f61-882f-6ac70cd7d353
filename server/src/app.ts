import process from 'node:process';

import express from 'express';
import type { Application } from 'express';

import { logger } from './config/index.js';
import { corsMiddleware, errorHandler } from './middleware/index.js';
import { authRoutes, routeRoutes, systemManageRoutes } from './routes/index.js';

/**
 * 创建 Express 应用
 * 保持与原有服务器配置完全一致
 */
export function createApp(): Application {
  const app = express();

  // 基础中间件，保持原有顺序
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(corsMiddleware);

  // 请求日志中间件
  app.use((req, _res, next) => {
    logger.http(`${req.method} ${req.url}`, {
      ip: req.ip || '',
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent') || ''
    });
    next();
  });

  // 健康检查端点，保持原有路径
  app.get('/health', (_req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });

  // API 路由，保持原有路径
  app.use('/auth', authRoutes);
  app.use('/route', routeRoutes);
  app.use('/systemManage', systemManageRoutes);

  // 404 处理
  app.use((_req, res) => {
    res.status(404).json({
      code: '404',
      data: null,
      message: 'API endpoint not found'
    });
  });

  // 错误处理中间件
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
export function startServer(): void {
  const app = createApp();
  const port = Number(process.env.PORT) || 3001;
  const host = process.env.HOST || 'localhost';

  app.listen(port, host, () => {
    logger.info(`Server is running on http://${host}:${port}`);
    logger.info(`Health check available at http://${host}:${port}/health`);
  });

  // 优雅关闭处理
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    process.exit(0);
  });

  // 未捕获异常处理
  process.on('uncaughtException', error => {
    logger.error('Uncaught Exception:', { error: error.message, stack: error.stack });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', { promise, reason });
    process.exit(1);
  });
}
