import { database } from '../config/index.js';
import type { AllRoleInfo, PaginationResult, RoleInfo, RoleSearchParams, ServiceResult, UpdateRoleButtonsRequest, UpdateRoleMenusRequest } from '../types/index.js';
import { formatApiResponse } from '../utils/index.js';

/**
 * 角色服务类
 * 处理角色相关的业务逻辑
 */
export class RoleService {
  /**
   * 获取角色列表
   */
  async getRoleList(params: RoleSearchParams): ServiceResult<PaginationResult<RoleInfo>> {
    const { current = 1, roleCode, roleName, size = 10, status } = params;

    const offset = (current - 1) * size;
    let whereClause = 'WHERE 1=1';
    const queryParams: unknown[] = [];
    let paramIndex = 1;

    // 构建查询条件
    if (roleCode) {
      whereClause += ` AND role_code ILIKE $${paramIndex}`;
      queryParams.push(`%${roleCode}%`);
      paramIndex += 1;
    }
    if (roleName) {
      whereClause += ` AND role_name ILIKE $${paramIndex}`;
      queryParams.push(`%${roleName}%`);
      paramIndex += 1;
    }
    if (status) {
      whereClause += ` AND status = $${paramIndex}`;
      queryParams.push(Number(status));
      paramIndex += 1;
    }

    const client = await database.connect();
    try {
      // 获取总数
      const countQuery = `SELECT COUNT(*) FROM meiye_roles ${whereClause}`;
      const countResult = await client.query(countQuery, queryParams);
      const total = Number(countResult.rows[0]?.count || 0);

      // 获取角色列表
      const dataQuery = `
        SELECT id, role_code, role_name, role_desc, status, home_path,
               menu_permissions, button_permissions, created_at, updated_at
        FROM meiye_roles
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(size, offset);

      const dataResult = await client.query(dataQuery, queryParams);

      // 格式化数据
      const formattedRoles = dataResult.rows.map(role =>
        formatApiResponse(role, 'role') as unknown as RoleInfo
      );

      return {
        current,
        records: formattedRoles,
        size,
        total
      };
    } finally {
      client.release();
    }
  }

  /**
   * 获取所有角色（简化信息）
   */
  async getAllRoles(): ServiceResult<AllRoleInfo[]> {
    const client = await database.connect();
    try {
      const query = `
        SELECT id, role_code, role_name
        FROM meiye_roles
        WHERE status = 1
        ORDER BY role_name
      `;

      const result = await client.query(query);

      const formattedRoles = result.rows.map(role =>
        formatApiResponse(role, 'common') as unknown as AllRoleInfo
      );

      return formattedRoles;
    } finally {
      client.release();
    }
  }

  /**
   * 删除角色
   */
  async deleteRole(id: number): ServiceResult<void> {
    const client = await database.connect();
    try {
      const result = await client.query('DELETE FROM meiye_roles WHERE id = $1', [id]);

      if (result.rowCount === 0) {
        throw new Error('Role not found');
      }
    } finally {
      client.release();
    }
  }

  /**
   * 批量删除角色
   */
  async batchDeleteRoles(ids: number[]): ServiceResult<void> {
    if (ids.length === 0) return;

    const client = await database.connect();
    try {
      const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
      const query = `DELETE FROM meiye_roles WHERE id IN (${placeholders})`;

      await client.query(query, ids);
    } finally {
      client.release();
    }
  }

  /**
   * 更新角色菜单权限
   */
  async updateRoleMenus(id: number, data: UpdateRoleMenusRequest): ServiceResult<void> {
    const { menuPermissions, homePath } = data;

    const client = await database.connect();
    try {
      const updateFields = ['menu_permissions = $2'];
      const queryParams: unknown[] = [id, JSON.stringify(menuPermissions)];
      let paramIndex = 3;

      if (homePath !== undefined) {
        updateFields.push(`home_path = $${paramIndex}`);
        queryParams.push(homePath);
        paramIndex += 1;
      }

      updateFields.push(`updated_at = $${paramIndex}`);
      queryParams.push(new Date().toISOString());

      const query = `
        UPDATE meiye_roles
        SET ${updateFields.join(', ')}
        WHERE id = $1
      `;

      const result = await client.query(query, queryParams);

      if (result.rowCount === 0) {
        throw new Error('Role not found');
      }
    } finally {
      client.release();
    }
  }

  /**
   * 更新角色按钮权限
   */
  async updateRoleButtons(id: number, data: UpdateRoleButtonsRequest): ServiceResult<void> {
    const { buttonPermissions } = data;

    const client = await database.connect();
    try {
      const query = `
        UPDATE meiye_roles
        SET button_permissions = $2, updated_at = $3
        WHERE id = $1
      `;

      const result = await client.query(query, [
        id,
        JSON.stringify(buttonPermissions),
        new Date().toISOString()
      ]);

      if (result.rowCount === 0) {
        throw new Error('Role not found');
      }
    } finally {
      client.release();
    }
  }
}
