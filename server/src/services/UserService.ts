import bcrypt from 'bcryptjs';

import { database } from '../config/index.js';
import type {
  AddUserRequest,
  AllRoleInfo,
  EditUserRequest,
  PaginationResult,
  ServiceResult,
  UserInfo,
  UserRecord,
  UserSearchParams
} from '../types/index.js';
import { formatApiResponse } from '../utils/index.js';

/**
 * 用户服务类
 * 保持与原有业务逻辑完全一致
 */
export class UserService {
  /**
   * 获取用户列表
   * 保持与原有查询逻辑完全一致
   */
  async getUserList(params: UserSearchParams): ServiceResult<PaginationResult<UserInfo>> {
    const { current = 1, nickName, size = 10, status, userEmail, userGender, userName, userPhone } = params;

    const offset = (current - 1) * size;
    let whereClause = 'WHERE 1=1';
    const queryParams: unknown[] = [];
    let paramIndex = 1;

    // 构建查询条件，保持原有逻辑
    if (userName) {
      whereClause += ` AND username ILIKE $${paramIndex}`;
      queryParams.push(`%${userName}%`);
      paramIndex += 1;
    }
    if (nickName) {
      whereClause += ` AND nick_name ILIKE $${paramIndex}`;
      queryParams.push(`%${nickName}%`);
      paramIndex += 1;
    }
    if (userEmail) {
      whereClause += ` AND email ILIKE $${paramIndex}`;
      queryParams.push(`%${userEmail}%`);
      paramIndex += 1;
    }
    if (userGender) {
      whereClause += ` AND user_gender = $${paramIndex}`;
      queryParams.push(userGender);
      paramIndex += 1;
    }
    if (userPhone) {
      whereClause += ` AND user_phone ILIKE $${paramIndex}`;
      queryParams.push(`%${userPhone}%`);
      paramIndex += 1;
    }
    if (status) {
      whereClause += ` AND status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex += 1;
    }

    const client = await database.connect();
    try {
      // 查询总数
      const countQuery = `SELECT COUNT(*) FROM meiye_users ${whereClause}`;
      const countResult = await client.query(countQuery, queryParams);
      const total = Number.parseInt(countResult.rows[0]?.count || '0', 10);

      // 查询数据，保持原有字段选择
      const dataQuery = `
        SELECT id, username, nick_name, email, user_phone, user_gender, status, roles, created_at, updated_at
        FROM meiye_users
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(size, offset);

      const dataResult = await client.query<UserRecord>(dataQuery, queryParams);

      // 格式化数据，保持原有格式化逻辑
      const formattedRecords = formatApiResponse(dataResult.rows, 'user') as unknown as UserInfo[];

      return {
        current,
        records: formattedRecords,
        size,
        total
      };
    } finally {
      client.release();
    }
  }

  /**
   * 添加用户
   * 保持与原有逻辑完全一致
   */
  async addUser(userData: AddUserRequest): ServiceResult<UserInfo> {
    const { nickName, password, status = 1, userEmail, userGender, userName, userPhone, userRoles } = userData;

    // 检查用户名是否已存在
    const client = await database.connect();
    try {
      const existingUser = await client.query('SELECT id FROM meiye_users WHERE username = $1', [userName]);
      if (existingUser.rows.length > 0) {
        throw new Error('Username already exists');
      }

      // 加密密码，保持原有加密逻辑
      const hashedPassword = await bcrypt.hash(password, 10);

      // 插入新用户，保持原有字段映射
      const insertQuery = `
        INSERT INTO meiye_users (username, password, nick_name, email, user_phone, user_gender, status, roles)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, username, nick_name, email, user_phone, user_gender, status, roles, created_at, updated_at
      `;

      const result = await client.query<UserRecord>(insertQuery, [
        userName,
        hashedPassword,
        nickName || null,
        userEmail || null,
        userPhone || null,
        userGender || null,
        status,
        userRoles || []
      ]);

      // 格式化返回数据
      const formattedUser = formatApiResponse(result.rows[0], 'user') as unknown as UserInfo;
      return formattedUser;
    } finally {
      client.release();
    }
  }

  /**
   * 编辑用户
   * 保持与原有逻辑完全一致
   */
  async editUser(id: number, userData: EditUserRequest): ServiceResult<UserInfo> {
    const { nickName, password, status, userEmail, userGender, userName, userPhone, userRoles } = userData;

    const client = await database.connect();
    try {
      // 检查用户是否存在
      const existingUser = await client.query('SELECT id FROM meiye_users WHERE id = $1', [id]);
      if (existingUser.rows.length === 0) {
        throw new Error('User not found');
      }

      // 检查用户名是否被其他用户使用
      const duplicateUser = await client.query('SELECT id FROM meiye_users WHERE username = $1 AND id != $2', [
        userName,
        id
      ]);
      if (duplicateUser.rows.length > 0) {
        throw new Error('Username already exists');
      }

      // 构建更新查询
      const updateFields: string[] = [];
      const updateParams: unknown[] = [];
      let paramIndex = 1;

      updateFields.push(`username = $${paramIndex}`);
      updateParams.push(userName);
      paramIndex += 1;

      if (password) {
        const hashedPassword = await bcrypt.hash(password, 10);
        updateFields.push(`password = $${paramIndex}`);
        updateParams.push(hashedPassword);
        paramIndex += 1;
      }

      updateFields.push(`nick_name = $${paramIndex}`);
      updateParams.push(nickName || null);
      paramIndex += 1;

      updateFields.push(`email = $${paramIndex}`);
      updateParams.push(userEmail || null);
      paramIndex += 1;

      updateFields.push(`user_phone = $${paramIndex}`);
      updateParams.push(userPhone || null);
      paramIndex += 1;

      updateFields.push(`user_gender = $${paramIndex}`);
      updateParams.push(userGender || null);
      paramIndex += 1;

      updateFields.push(`status = $${paramIndex}`);
      updateParams.push(status);
      paramIndex += 1;

      updateFields.push(`roles = $${paramIndex}`);
      updateParams.push(userRoles || []);
      paramIndex += 1;

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

      const updateQuery = `
        UPDATE meiye_users
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, username, nick_name, email, user_phone, user_gender, status, roles, created_at, updated_at
      `;
      updateParams.push(id);

      const result = await client.query<UserRecord>(updateQuery, updateParams);

      // 格式化返回数据
      const formattedUser = formatApiResponse(result.rows[0], 'user') as unknown as UserInfo;
      return formattedUser;
    } finally {
      client.release();
    }
  }

  /**
   * 删除用户
   * 保持与原有逻辑完全一致
   */
  async deleteUser(id: number): ServiceResult<void> {
    const client = await database.connect();
    try {
      const result = await client.query('DELETE FROM meiye_users WHERE id = $1', [id]);

      if (result.rowCount === 0) {
        throw new Error('User not found');
      }
    } finally {
      client.release();
    }
  }

  /**
   * 批量删除用户
   */
  async batchDeleteUsers(ids: number[]): ServiceResult<void> {
    if (ids.length === 0) return;

    const client = await database.connect();
    try {
      const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
      const query = `DELETE FROM meiye_users WHERE id IN (${placeholders})`;

      await client.query(query, ids);
    } finally {
      client.release();
    }
  }

  /**
   * 获取所有角色
   * 保持与原有逻辑完全一致
   */
  async getAllRoles(): ServiceResult<AllRoleInfo[]> {
    const client = await database.connect();
    try {
      const result = await client.query(`
        SELECT id, role_code, role_name
        FROM meiye_roles
        WHERE status = 1
        ORDER BY created_at ASC
      `);

      // 格式化数据，保持原有字段映射
      const roles = result.rows.map(row => ({
        id: row.id,
        roleCode: row.role_code,
        roleName: row.role_name
      }));

      return roles;
    } finally {
      client.release();
    }
  }
}
