// API 相关类型
export type {
  AddUserRequest,
  AllRoleInfo,
  ApiResponse,
  ButtonPermission,
  CommonPaginationParams,
  EditUserRequest,
  // 认证相关
  LoginRequest,
  LoginResponse,
  // 菜单相关
  MenuTreeNode,
  PaginationResponse,
  // 角色相关
  RoleInfo,
  RoleListResponse,
  RoleSearchParams,
  UpdateRoleButtonsRequest,
  UpdateRoleMenusRequest,
  // 用户相关
  UserInfo,
  UserInfoResponse,
  UserListResponse,
  UserRouteResponse,
  UserSearchParams
} from './api';

// 通用类型
export type {
  AppError,
  ControllerMethod,
  DeepReadonly,
  ErrorMiddleware,
  FieldMapping,
  FilterConfig,
  LogContext,
  LogLevel,
  Middleware,
  Optional,
  QueryResult,
  RepositoryResult,
  Required,
  ServiceResult,
  SortConfig,
  StrictObject
} from './common';

// 枚举类型
export { ApiCode, HttpStatus, RoleStatus, UserGender, UserStatus } from './common';

// 配置相关类型
export type {
  AppConfig,
  DatabaseConfig as ConfigDatabaseConfig,
  EnvironmentVariables,
  JwtConfig,
  LoggerConfig,
  RedisConfig,
  ServerConfig
} from './config';

/**
 * 类型定义统一导出
 */

// 数据库相关类型
export type {
  BaseRecord,
  DatabaseConfig,
  IngredientRecord,
  PaginationParams,
  PaginationResult,
  ProductRecord,
  QueryParams,
  RoleRecord,
  UserRecord
} from './database';
