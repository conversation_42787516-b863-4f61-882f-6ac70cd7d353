/**
 * 通用类型定义
 */

import type { NextFunction, Request, Response } from 'express';

/** 严格的对象类型 */
export type StrictObject = Record<string, unknown>;

/** 只读深度类型 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/** 可选属性类型 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/** 必需属性类型 */
export type Required<T, K extends keyof T> = T & { [P in K]-?: T[P] };

/** Express 中间件类型 */
export type Middleware = (req: Request, res: Response, next: NextFunction) => void | Promise<void>;

/** Express 错误处理中间件类型 */
// eslint-disable-next-line max-params -- Express error middleware requires exactly 4 parameters
export type ErrorMiddleware = (error: Error, req: Request, res: Response, next: NextFunction) => void | Promise<void>;

/** 控制器方法类型 */
export type ControllerMethod = (req: Request, res: Response, next: NextFunction) => Promise<void>;

/** 服务方法返回类型 */
export type ServiceResult<T> = Promise<T>;

/** 仓储方法返回类型 */
export type RepositoryResult<T> = Promise<T>;

/** 数据库查询结果类型 */
export interface QueryResult<T = unknown> {
  readonly rowCount: number;
  readonly rows: readonly T[];
}

/** 错误类型 */
export interface AppError extends Error {
  readonly code: string;
  readonly isOperational: boolean;
  readonly statusCode: number;
}

/** 日志级别 */
export type LogLevel = 'debug' | 'error' | 'info' | 'warn';

/** 日志上下文 */
export interface LogContext {
  readonly ip?: string;
  readonly method?: string;
  readonly requestId?: string;
  readonly url?: string;
  readonly userAgent?: string;
  readonly userId?: string;
  readonly [key: string]: unknown;
}

/** HTTP 状态码 */
export const HttpStatus = {
  BAD_REQUEST: 400,
  CONFLICT: 409,
  CREATED: 201,
  FORBIDDEN: 403,
  INTERNAL_SERVER_ERROR: 500,
  NO_CONTENT: 204,
  NOT_FOUND: 404,
  OK: 200,
  UNAUTHORIZED: 401
} as const;

/** API 响应码 */
export const ApiCode = {
  BAD_REQUEST: '400',
  CONFLICT: '409',
  FORBIDDEN: '403',
  INTERNAL_ERROR: '500',
  NOT_FOUND: '404',
  SUCCESS: '0000',
  UNAUTHORIZED: '401'
} as const;

/** 用户状态 */
export const UserStatus = {
  ACTIVE: 1,
  INACTIVE: 2
} as const;

/** 角色状态 */
export const RoleStatus = {
  ACTIVE: 1,
  INACTIVE: 2
} as const;

/** 用户性别 */
export const UserGender = {
  FEMALE: 2,
  MALE: 1
} as const;

/** 字段映射配置 */
export interface FieldMapping {
  readonly [dbField: string]: string;
}

/** 排序配置 */
export interface SortConfig {
  readonly field: string;
  readonly order: 'ASC' | 'DESC';
}

/** 筛选配置 */
export interface FilterConfig {
  readonly field: string;
  readonly operator: '!=' | '<' | '<=' | '=' | '>' | '>=' | 'IN' | 'LIKE' | 'NOT IN';
  readonly value: unknown;
}
