/**
 * API 相关类型定义
 */

/** API 响应基础结构 */
export interface ApiResponse<T = unknown> {
  readonly code: string;
  readonly data: T;
  readonly message: string;
}

/** 通用分页参数 */
export interface CommonPaginationParams {
  readonly current: number;
  readonly size: number;
}

/** 分页响应数据 */
export interface PaginationResponse<T> {
  readonly current: number;
  readonly records: readonly T[];
  readonly size: number;
  readonly total: number;
}

// ==================== 用户相关 API 类型 ====================

/** 用户信息 */
export interface UserInfo {
  readonly createTime: string;
  readonly id: number;
  readonly nickName: string | null;
  readonly status: '1' | '2';
  readonly updateTime: string;
  readonly userEmail: string | null;
  readonly userGender: '1' | '2' | null;
  readonly userName: string;
  readonly userPhone: string | null;
  readonly userRoles: readonly string[];
}

/** 用户搜索参数 */
export interface UserSearchParams extends CommonPaginationParams {
  readonly nickName?: string;
  readonly status?: '1' | '2';
  readonly userEmail?: string;
  readonly userGender?: '1' | '2';
  readonly userName?: string;
  readonly userPhone?: string;
}

/** 用户列表响应 */
export type UserListResponse = PaginationResponse<UserInfo>;

/** 添加用户请求 */
export interface AddUserRequest {
  readonly nickName?: string;
  readonly password: string;
  readonly status?: '1' | '2';
  readonly userEmail?: string;
  readonly userGender?: '1' | '2';
  readonly userName: string;
  readonly userPhone?: string;
  readonly userRoles: readonly string[];
}

/** 编辑用户请求 */
export interface EditUserRequest {
  readonly nickName?: string;
  readonly password?: string;
  readonly status?: '1' | '2';
  readonly userEmail?: string;
  readonly userGender?: '1' | '2';
  readonly userName: string;
  readonly userPhone?: string;
  readonly userRoles: readonly string[];
}

// ==================== 角色相关 API 类型 ====================

/** 角色信息 */
export interface RoleInfo {
  readonly buttonPermissions: readonly string[];
  readonly createTime: string;
  readonly homePath: string;
  readonly id: number;
  readonly menuPermissions: readonly string[];
  readonly roleCode: string;
  readonly roleDesc: string | null;
  readonly roleName: string;
  readonly status: '1' | '2';
  readonly updateTime: string;
}

/** 角色搜索参数 */
export interface RoleSearchParams extends CommonPaginationParams {
  readonly roleCode?: string;
  readonly roleName?: string;
  readonly status?: '1' | '2';
}

/** 角色列表响应 */
export type RoleListResponse = PaginationResponse<RoleInfo>;

/** 所有角色信息 */
export interface AllRoleInfo {
  readonly id: number;
  readonly roleCode: string;
  readonly roleName: string;
}

/** 更新角色菜单权限请求 */
export interface UpdateRoleMenusRequest {
  readonly homePath?: string;
  readonly menuPermissions: readonly string[];
}

/** 更新角色按钮权限请求 */
export interface UpdateRoleButtonsRequest {
  readonly buttonPermissions: readonly string[];
}

// ==================== 菜单相关 API 类型 ====================

/** 菜单树节点 */
export interface MenuTreeNode {
  readonly children?: readonly MenuTreeNode[];
  readonly key: string;
  readonly title: string;
}

/** 按钮权限 */
export interface ButtonPermission {
  readonly key: string;
  readonly title: string;
}

// ==================== 认证相关 API 类型 ====================

/** 登录请求 */
export interface LoginRequest {
  readonly password: string;
  readonly userName: string;
}

/** 登录响应 */
export interface LoginResponse {
  readonly refreshToken: string;
  readonly token: string;
}

/** 用户信息响应 */
export interface UserInfoResponse {
  readonly buttons: readonly string[];
  readonly roles: readonly string[];
  readonly userId: string;
  readonly userName: string;
}

/** 用户路由响应 */
export interface UserRouteResponse {
  readonly home: string;
  readonly routes: readonly string[];
}
