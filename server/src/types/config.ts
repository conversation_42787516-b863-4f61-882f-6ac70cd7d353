/**
 * 配置相关类型定义
 * 严格映射现有配置项，不新增任何配置
 */

/** 数据库配置 */
export interface DatabaseConfig {
  readonly connectionTimeoutMillis: number;
  readonly database: string;
  readonly host: string;
  readonly idleTimeoutMillis: number;
  readonly max: number;
  readonly password: string;
  readonly port: number;
  readonly user: string;
}

/** Redis 配置 */
export interface RedisConfig {
  readonly db: number;
  readonly host: string;
  readonly keyPrefix: string;
  readonly password?: string;
  readonly port: number;
}

/** JWT 配置 */
export interface JwtConfig {
  readonly expiresIn: string;
  readonly refreshExpiresIn: string;
  readonly refreshSecret: string;
  readonly secret: string;
}

/** 服务器配置 */
export interface ServerConfig {
  readonly corsOrigin: string | readonly string[];
  readonly env: 'development' | 'production' | 'test';
  readonly host: string;
  readonly port: number;
}

/** 日志配置 */
export interface LoggerConfig {
  readonly filename?: string;
  readonly format: 'json' | 'simple';
  readonly level: 'debug' | 'error' | 'info' | 'warn';
  readonly maxFiles?: number;
  readonly maxSize?: string;
}

/** 应用配置 */
export interface AppConfig {
  readonly database: DatabaseConfig;
  readonly jwt: JwtConfig;
  readonly logger: LoggerConfig;
  readonly redis: RedisConfig;
  readonly server: ServerConfig;
}

/** 环境变量类型 */
export interface EnvironmentVariables {
  readonly CORS_ORIGIN: string;
  readonly DB_HOST: string;
  readonly DB_NAME: string;
  readonly DB_PASSWORD: string;
  readonly DB_PORT: string;
  readonly DB_USER: string;
  readonly HOST: string;
  readonly JWT_EXPIRES_IN: string;
  readonly JWT_REFRESH_EXPIRES_IN: string;
  readonly JWT_REFRESH_SECRET: string;
  readonly JWT_SECRET: string;
  readonly LOG_LEVEL: string;
  readonly NODE_ENV: 'development' | 'production' | 'test';
  readonly PORT: string;
  readonly REDIS_HOST: string;
  readonly REDIS_PASSWORD?: string;
  readonly REDIS_PORT: string;
}
