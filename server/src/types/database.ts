/**
 * 数据库表结构类型定义
 * 严格映射现有数据库表结构，不新增任何字段
 */

/** 通用记录基础字段 */
export interface BaseRecord {
  readonly created_at: string;
  readonly id: number;
  readonly updated_at: string;
}

/** 用户表结构 */
export interface UserRecord extends BaseRecord {
  readonly buttons: readonly string[];
  readonly email: string | null;
  readonly nick_name: string | null;
  readonly password: string;
  readonly roles: readonly string[];
  readonly status: 1 | 2;
  readonly user_gender: 1 | 2 | null;
  readonly user_phone: string | null;
  readonly username: string;
}

/** 角色表结构 */
export interface RoleRecord extends BaseRecord {
  readonly button_permissions: readonly string[];
  readonly home_path: string;
  readonly menu_permissions: readonly string[];
  readonly role_code: string;
  readonly role_desc: string | null;
  readonly role_name: string;
  readonly status: 1 | 2;
}

/** 产品表结构 */
export interface ProductRecord extends BaseRecord {
  readonly all_record_efficacy: string;
  readonly brand: string;
  readonly category: string;
  readonly cosmetic_id: string;
  readonly cosmetics_type: string;
  readonly launch_date: string;
  readonly manufacturer: string;
  readonly name: string;
  readonly obm_name: string;
  readonly place: string;
  readonly province: string;
}

/** 成分表结构 */
export interface IngredientRecord {
  readonly cosmetic_id: string;
  readonly material_name: string;
  readonly position: number;
}

/** 数据库查询参数类型 */
export interface QueryParams {
  readonly [key: string]: string | number | boolean | null | undefined;
}

/** 分页查询参数 */
export interface PaginationParams {
  readonly current: number;
  readonly size: number;
}

/** 分页查询结果 */
export interface PaginationResult<T> {
  readonly current: number;
  readonly records: readonly T[];
  readonly size: number;
  readonly total: number;
}

/** 数据库连接配置 */
export interface DatabaseConfig {
  readonly connectionTimeoutMillis: number;
  readonly database: string;
  readonly host: string;
  readonly idleTimeoutMillis: number;
  readonly max: number;
  readonly password: string;
  readonly port: number;
  readonly user: string;
}
