import type { NextFunction, Request, Response } from 'express';

import { logger } from '../config/index.js';
import { RoleService } from '../services/RoleService.js';
import { UserService } from '../services/UserService.js';
import type { AddUserRequest, ControllerMethod, EditUserRequest, RoleSearchParams, UpdateRoleButtonsRequest, UpdateRoleMenusRequest, UserSearchParams } from '../types/index.js';
import { responseHelper } from '../utils/index.js';

/**
 * 系统管理控制器
 * 保持与原有 API 接口完全一致
 */
export class SystemManageController {
  private userService: UserService;
  private roleService: RoleService;

  constructor() {
    this.userService = new UserService();
    this.roleService = new RoleService();
  }

  /**
   * 获取用户列表
   * 保持与原有接口完全一致
   */
  public getUserList: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const params = req.query as unknown as UserSearchParams;

      // 参数类型转换，保持原有逻辑
      const searchParams: UserSearchParams = {
        current: Number(params.current) || 1,
        nickName: params.nickName as string,
        size: Number(params.size) || 10,
        userEmail: params.userEmail as string,
        userName: params.userName as string,
        userPhone: params.userPhone as string,
        ...(params.userGender && { userGender: params.userGender as '1' | '2' }),
        ...(params.status && { status: params.status as '1' | '2' })
      };

      const result = await this.userService.getUserList(searchParams);
      responseHelper.paginated(res, result.records, result.total, result.current, result.size);
    } catch (error) {
      logger.api.error('Get user list error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 添加用户
   * 保持与原有接口完全一致
   */
  public addUser: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userData = req.body as AddUserRequest;
      const result = await this.userService.addUser(userData);
      responseHelper.success(res, result, 'User added successfully');
    } catch (error) {
      logger.api.error('Add user error:', { error: (error as Error).message });

      if ((error as Error).message === 'Username already exists') {
        responseHelper.conflict(res, 'Username already exists');
        return;
      }

      next(error);
    }
  };

  /**
   * 编辑用户
   * 保持与原有接口完全一致
   */
  public editUser: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const id = Number(req.params.id);
      const userData = req.body as EditUserRequest;

      if (!id || Number.isNaN(id)) {
        responseHelper.badRequest(res, 'Invalid user ID');
        return;
      }

      const result = await this.userService.editUser(id, userData);
      responseHelper.success(res, result, 'User updated successfully');
    } catch (error) {
      logger.api.error('Edit user error:', { error: (error as Error).message });

      const errorMessage = (error as Error).message;
      if (errorMessage === 'User not found') {
        responseHelper.notFound(res, 'User not found');
        return;
      }
      if (errorMessage === 'Username already exists') {
        responseHelper.conflict(res, 'Username already exists');
        return;
      }

      next(error);
    }
  };

  /**
   * 删除用户
   * 保持与原有接口完全一致
   */
  public deleteUser: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const id = Number(req.params.id);

      if (!id || Number.isNaN(id)) {
        responseHelper.badRequest(res, 'Invalid user ID');
        return;
      }

      await this.userService.deleteUser(id);
      responseHelper.success(res, null, 'User deleted successfully');
    } catch (error) {
      logger.api.error('Delete user error:', { error: (error as Error).message });

      if ((error as Error).message === 'User not found') {
        responseHelper.notFound(res, 'User not found');
        return;
      }

      next(error);
    }
  };

  /**
   * 批量删除用户
   */
  public batchDeleteUsers: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { ids } = req.body as { ids: number[] };

      if (!Array.isArray(ids) || ids.length === 0) {
        responseHelper.badRequest(res, 'Invalid user IDs');
        return;
      }

      await this.userService.batchDeleteUsers(ids);
      responseHelper.success(res, null, `Successfully deleted ${ids.length} users`);
    } catch (error) {
      logger.api.error('Batch delete users error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 获取角色列表
   * 支持分页和搜索
   */
  public getRoleList: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const params = req.query as unknown as RoleSearchParams;

      // 参数类型转换
      const searchParams: RoleSearchParams = {
        current: Number(params.current) || 1,
        roleCode: params.roleCode as string,
        roleName: params.roleName as string,
        size: Number(params.size) || 10,
        ...(params.status && { status: params.status as '1' | '2' })
      };

      const result = await this.roleService.getRoleList(searchParams);
      responseHelper.paginated(res, result.records, result.total, result.current, result.size);
    } catch (error) {
      logger.api.error('Get role list error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 获取所有角色
   * 保持与原有接口完全一致
   */
  public getAllRoles: ControllerMethod = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const roles = await this.roleService.getAllRoles();
      responseHelper.success(res, roles, 'Success');
    } catch (error) {
      logger.api.error('Get all roles error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 获取菜单树
   * 保持与原有接口完全一致
   */
  public static getMenuTree: ControllerMethod = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // 保持原有的静态菜单树结构
      const menuTree = [
        {
          children: [
            {
              key: '/dashboard/analysis',
              title: '分析页'
            },
            {
              key: '/dashboard/workbench',
              title: '工作台'
            }
          ],
          key: '/dashboard',
          title: '仪表盘'
        },
        {
          children: [
            {
              children: [
                {
                  key: '/function/tab/tab-detail',
                  title: '隐藏子菜单'
                },
                {
                  key: '/function/tab/tab-detail-two',
                  title: '菜单二'
                },
                {
                  key: '/function/tab/tab-detail-three',
                  title: '菜单三'
                }
              ],
              key: '/function/tab',
              title: '标签页'
            },
            {
              key: '/function/tab/tab-multi-detail',
              title: '隐藏子菜单'
            },
            {
              key: '/function/request',
              title: '请求'
            },
            {
              key: '/function/toggle-auth',
              title: '切换权限'
            },
            {
              key: '/function/super',
              title: '超级管理员可见'
            },
            {
              key: '/function/event-bus',
              title: '事件总线演示'
            },
            {
              key: '/function/request-hook',
              title: 'useRequest 演示'
            }
          ],
          key: '/function',
          title: '系统功能'
        },
        {
          children: [
            {
              children: [
                {
                  key: '/multi-menu/first/child',
                  title: '菜单一子菜单'
                }
              ],
              key: '/multi-menu/first',
              title: '菜单一'
            },
            {
              children: [
                {
                  children: [
                    {
                      key: '/multi-menu/second/child/home',
                      title: '菜单二子菜单首页'
                    }
                  ],
                  key: '/multi-menu/second/child',
                  title: '菜单二子菜单'
                }
              ],
              key: '/multi-menu/second',
              title: '菜单二'
            }
          ],
          key: '/multi-menu',
          title: '多级菜单'
        },
        {
          children: [
            {
              children: [
                {
                  key: '/projects/:id/edit/:editId',
                  title: '多级动态路由编辑详情'
                }
              ],
              key: '/projects/:id',
              title: '多级动态路由详情'
            },
            {
              key: '/projects/:id/edit',
              title: '多级动态路由编辑'
            }
          ],
          key: '/projects',
          title: '多级动态路由'
        },
        {
          children: [
            {
              children: [
                {
                  key: '/manage/user/:id',
                  title: '用户详情'
                }
              ],
              key: '/manage/user',
              title: '用户管理'
            },
            {
              children: [
                {
                  key: '/manage/role/:id',
                  title: '角色管理详情'
                }
              ],
              key: '/manage/role',
              title: '角色管理'
            },
            {
              key: '/manage/menu',
              title: '菜单管理'
            }
          ],
          key: '/manage',
          title: '系统管理'
        },
        {
          key: '/user-center',
          title: '个人中心'
        },
        {
          key: '/about',
          title: '关于'
        },
        {
          children: [
            {
              key: '/exception/403',
              title: '无权限'
            },
            {
              key: '/exception/404',
              title: '页面不存在'
            },
            {
              key: '/exception/500',
              title: '服务器错误'
            }
          ],
          key: '/exception',
          title: '异常页'
        },
        {
          children: [
            {
              key: '/others/ui',
              title: 'UI'
            },
            {
              key: '/403',
              title: '无权限'
            },
            {
              key: '/404',
              title: '页面不存在'
            },
            {
              key: '/500',
              title: '服务器错误'
            },
            {
              key: '/others/iframe-page',
              title: '外链页面'
            }
          ],
          key: '/others',
          title: '其他页面'
        }
      ];

      responseHelper.success(res, menuTree, 'Success');
    } catch (error) {
      logger.api.error('Get menu tree error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 删除角色
   */
  public deleteRole: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const id = Number(req.params.id);

      if (!id || Number.isNaN(id)) {
        responseHelper.badRequest(res, 'Invalid role ID');
        return;
      }

      await this.roleService.deleteRole(id);
      responseHelper.success(res, null, 'Role deleted successfully');
    } catch (error) {
      logger.api.error('Delete role error:', { error: (error as Error).message });

      if ((error as Error).message === 'Role not found') {
        responseHelper.notFound(res, 'Role not found');
        return;
      }

      next(error);
    }
  };

  /**
   * 批量删除角色
   */
  public batchDeleteRoles: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { ids } = req.body as { ids: number[] };

      if (!Array.isArray(ids) || ids.length === 0) {
        responseHelper.badRequest(res, 'Invalid role IDs');
        return;
      }

      await this.roleService.batchDeleteRoles(ids);
      responseHelper.success(res, null, `Successfully deleted ${ids.length} roles`);
    } catch (error) {
      logger.api.error('Batch delete roles error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 更新角色菜单权限
   */
  public updateRoleMenus: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const id = Number(req.params.id);
      const data = req.body as UpdateRoleMenusRequest;

      if (!id || Number.isNaN(id)) {
        responseHelper.badRequest(res, 'Invalid role ID');
        return;
      }

      if (!Array.isArray(data.menuPermissions)) {
        responseHelper.badRequest(res, 'Invalid menu permissions');
        return;
      }

      await this.roleService.updateRoleMenus(id, data);
      responseHelper.success(res, null, 'Role menu permissions updated successfully');
    } catch (error) {
      logger.api.error('Update role menus error:', { error: (error as Error).message });

      if ((error as Error).message === 'Role not found') {
        responseHelper.notFound(res, 'Role not found');
        return;
      }

      next(error);
    }
  };

  /**
   * 更新角色按钮权限
   */
  public updateRoleButtons: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const id = Number(req.params.id);
      const data = req.body as UpdateRoleButtonsRequest;

      if (!id || Number.isNaN(id)) {
        responseHelper.badRequest(res, 'Invalid role ID');
        return;
      }

      if (!Array.isArray(data.buttonPermissions)) {
        responseHelper.badRequest(res, 'Invalid button permissions');
        return;
      }

      await this.roleService.updateRoleButtons(id, data);
      responseHelper.success(res, null, 'Role button permissions updated successfully');
    } catch (error) {
      logger.api.error('Update role buttons error:', { error: (error as Error).message });

      if ((error as Error).message === 'Role not found') {
        responseHelper.notFound(res, 'Role not found');
        return;
      }

      next(error);
    }
  };

  /**
   * 获取菜单列表 v2
   */
  public static getMenuListV2: ControllerMethod = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // 返回与菜单树相同的数据结构，但格式化为列表
      const menuList = [
        { id: '1', key: '/dashboard', title: '仪表盘', type: '1' },
        { id: '2', key: '/dashboard/analysis', title: '分析页', type: '2', parentId: '1' },
        { id: '3', key: '/dashboard/workbench', title: '工作台', type: '2', parentId: '1' },
        { id: '4', key: '/function', title: '系统功能', type: '1' },
        { id: '5', key: '/function/tab', title: '标签页', type: '2', parentId: '4' },
        { id: '6', key: '/function/request', title: '请求', type: '2', parentId: '4' },
        { id: '7', key: '/manage', title: '系统管理', type: '1' },
        { id: '8', key: '/manage/user', title: '用户管理', type: '2', parentId: '7' },
        { id: '9', key: '/manage/role', title: '角色管理', type: '2', parentId: '7' },
        { id: '10', key: '/manage/menu', title: '菜单管理', type: '2', parentId: '7' }
      ];

      responseHelper.success(res, menuList, 'Success');
    } catch (error) {
      logger.api.error('Get menu list v2 error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 获取所有页面
   */
  public static getAllPages: ControllerMethod = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // 返回所有可用的页面路径
      const pages = [
        '/dashboard/analysis',
        '/dashboard/workbench',
        '/function/tab',
        '/function/request',
        '/function/toggle-auth',
        '/function/super',
        '/function/event-bus',
        '/function/request-hook',
        '/manage/user',
        '/manage/role',
        '/manage/menu',
        '/user-center',
        '/about'
      ];

      responseHelper.success(res, pages, 'Success');
    } catch (error) {
      logger.api.error('Get all pages error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 获取所有按钮权限
   * 保持与原有接口完全一致
   */
  public static getAllButtons: ControllerMethod = async (
    _req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // 保持原有的静态按钮权限结构
      const buttons = [
        { key: 'B_CODE1', title: '按钮1' },
        { key: 'B_CODE2', title: '按钮2' },
        { key: 'B_CODE3', title: '按钮3' },
        { key: 'B_CODE4', title: '按钮4' },
        { key: 'B_CODE5', title: '按钮5' }
      ];

      responseHelper.success(res, buttons, 'Success');
    } catch (error) {
      logger.api.error('Get all buttons error:', { error: (error as Error).message });
      next(error);
    }
  };
}
