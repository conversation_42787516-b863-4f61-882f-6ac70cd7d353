import process from 'node:process';

import bcrypt from 'bcryptjs';
import type { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';

import { database, logger } from '../config';
import type {
  ControllerMethod,
  LoginRequest,
  LoginResponse,
  UserInfoResponse,
  UserRecord,
  UserRouteResponse
} from '../types';
import { mapUserToFrontend, responseHelper } from '../utils';

/**
 * JWT 载荷类型
 */
interface JwtPayload {
  readonly exp: number;
  readonly iat: number;
  readonly userId: number;
}

/**
 * 认证控制器
 * 保持与原有 API 接口完全一致
 */
export class AuthController {
  /**
   * 生成 JWT tokens
   * 保持与原有逻辑完全一致
   */
  private static generateTokens(userId: number): { refreshToken: string; token: string } {
    const secret = process.env.JWT_SECRET!;

    const token = jwt.sign({ userId }, secret, {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    } as jwt.SignOptions);

    const refreshToken = jwt.sign({ userId }, secret, {
      expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d'
    } as jwt.SignOptions);

    return { refreshToken, token };
  }

  /**
   * 用户登录
   * 保持与原有接口完全一致
   */
  public login: ControllerMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { password, userName } = req.body as LoginRequest;

      if (!userName || !password) {
        responseHelper.badRequest(res, 'Username and password are required');
        return;
      }

      const client = await database.connect();
      try {
        const result = await client.query<UserRecord>('SELECT * FROM meiye_users WHERE username = $1', [userName]);

        if (result.rows.length === 0) {
          responseHelper.unauthorized(res, 'Invalid username or password');
          return;
        }

        const user = result.rows[0]!;
        const isValidPassword = await bcrypt.compare(password, user.password);

        if (!isValidPassword) {
          responseHelper.unauthorized(res, 'Invalid username or password');
          return;
        }

        const { refreshToken, token } = AuthController.generateTokens(user.id);

        const loginResponse: LoginResponse = {
          refreshToken,
          token
        };

        responseHelper.success(res, loginResponse, 'Login successful');
      } finally {
        client.release();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.auth.error(`Login error: ${errorMessage}`, {
        error: errorMessage,
        stack: errorStack,
        username: req.body?.userName
      });
      next(error);
    }
  };

  /**
   * 获取用户信息
   * 保持与原有接口完全一致
   */
  public static getUserInfo: ControllerMethod = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const user = req.user!;

      // 使用统一的字段映射，保持原有逻辑
      const userInfo = mapUserToFrontend(user as any);

      const userInfoResponse: UserInfoResponse = {
        buttons: (userInfo as any)?.buttons || [],
        roles: (userInfo as any)?.roles || ['R_USER'],
        userId: (userInfo as any)?.userId?.toString() || (userInfo as any)?.id?.toString() || '',
        userName: (userInfo as any)?.userName || (userInfo as any)?.username || ''
      };

      responseHelper.success(res, userInfoResponse, 'Success');
    } catch (error) {
      logger.auth.error('Get user info error:', { error: (error as Error).message });
      next(error);
    }
  };

  /**
   * 刷新 token
   * 保持与原有接口完全一致
   */
  public refreshToken: ControllerMethod = async (req: Request, res: Response): Promise<void> => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        responseHelper.badRequest(res, 'Refresh token is required');
        return;
      }

      const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET!) as JwtPayload;
      const { refreshToken: newRefreshToken, token: newToken } = AuthController.generateTokens(decoded.userId);

      const tokenResponse: LoginResponse = {
        refreshToken: newRefreshToken,
        token: newToken
      };

      responseHelper.success(res, tokenResponse, 'Token refreshed successfully');
    } catch (error) {
      logger.auth.error('Refresh token error:', { error: (error as Error).message });

      // 保持原有的错误响应格式
      res.status(403).json({
        code: '403',
        data: null,
        message: 'Invalid refresh token'
      });
    }
  };

  /**
   * 获取用户路由
   * 根据用户角色返回对应的路由路径
   */
  public static getUserRoute: ControllerMethod = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const user = req.user!;
      const userRoles = user.roles || [];

      // 定义角色对应的路由路径
      const roleRoutes: Record<string, string[]> = {
        super: [
          '/dashboard',
          '/dashboard/analysis',
          '/dashboard/workbench',
          '/function',
          '/function/tab',
          '/function/multi-tab',
          '/function/hide-child',
          '/function/request',
          '/function/super-page',
          '/manage',
          '/manage/user',
          '/manage/role',
          '/multi-menu',
          '/multi-menu/first',
          '/multi-menu/first/child',
          '/multi-menu/second',
          '/multi-menu/second/child',
          '/exception',
          '/exception/403',
          '/exception/404',
          '/exception/500'
        ],
        admin: [
          '/dashboard',
          '/dashboard/analysis',
          '/dashboard/workbench',
          '/function',
          '/function/tab',
          '/function/multi-tab',
          '/function/hide-child',
          '/function/request',
          '/manage',
          '/manage/user',
          '/exception',
          '/exception/403',
          '/exception/404',
          '/exception/500'
        ],
        user: [
          '/dashboard',
          '/dashboard/analysis',
          '/dashboard/workbench',
          '/exception',
          '/exception/403',
          '/exception/404',
          '/exception/500'
        ]
      };

      // 根据用户角色合并路由
      const userRoutes = new Set<string>();

      userRoles.forEach(role => {
        const routes = roleRoutes[role] || [];
        routes.forEach(route => userRoutes.add(route));
      });

      const userRouteResponse: UserRouteResponse = {
        home: '/dashboard/analysis',
        routes: Array.from(userRoutes)
      };

      responseHelper.success(res, userRouteResponse, 'Success');
    } catch (error) {
      logger.auth.error('Get user route error:', { error: (error as Error).message });
      next(error);
    }
  };
}
