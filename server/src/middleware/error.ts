import type { NextFunction, Request, Response } from 'express';

import { logger } from '../config/index.js';
import type { AppError } from '../types/index.js';
import { responseHelper } from '../utils/index.js';

/**
 * 应用错误类
 */
export class ApplicationError extends Error implements AppError {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode = 500, code = '500', isOperational = true) {
    super(message);
    this.name = 'ApplicationError';
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 错误处理中间件
 * 统一处理应用中的错误
 */
export const errorHandler = (
  error: Error | ApplicationError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // 记录错误日志
  logger.error('Unhandled error occurred', {
    error: error.message,
    ip: req.ip || '',
    method: req.method,
    stack: error.stack,
    url: req.url,
    userAgent: req.get('User-Agent') || ''
  });

  // 如果是应用错误，使用其状态码和消息
  if (error instanceof ApplicationError) {
    responseHelper.error(res, error.code, error.message, error.statusCode);
    return;
  }

  // 处理 JWT 错误
  if (error.name === 'JsonWebTokenError') {
    responseHelper.unauthorized(res, 'Invalid token');
    return;
  }

  if (error.name === 'TokenExpiredError') {
    responseHelper.unauthorized(res, 'Token expired');
    return;
  }

  // 处理数据库错误
  if (error.message?.includes('duplicate key')) {
    responseHelper.conflict(res, 'Resource already exists');
    return;
  }

  // 默认服务器错误
  responseHelper.internalError(res, 'Internal server error');
};
