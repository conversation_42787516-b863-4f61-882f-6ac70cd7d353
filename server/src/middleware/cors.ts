import process from 'node:process';

import type { NextFunction, Request, Response } from 'express';

/**
 * CORS 中间件
 * 保持与原有 CORS 配置完全一致
 */
export const corsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // 设置 CORS 头，保持原有配置
  res.header('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
};
