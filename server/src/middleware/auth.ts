import process from 'node:process';

import type { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';

import { database } from '../config/index.js';
import type { UserRecord } from '../types/index.js';
import { responseHelper } from '../utils/index.js';

/**
 * 扩展 Request 接口以包含用户信息
 */
declare global {
  namespace Express {
    interface Request {
      user?: UserRecord;
    }
  }
}

/**
 * JWT 载荷类型
 */
interface JwtPayload {
  readonly exp: number;
  readonly iat: number;
  readonly userId: number;
}

/**
 * 认证中间件
 * 保持与原有逻辑完全一致
 */
export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    responseHelper.unauthorized(res, 'Access token required');
    return;
  }

  try {
    // 验证 JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;

    // 验证用户是否仍然存在，保持原有查询逻辑
    const client = await database.connect();
    try {
      const result = await client.query<UserRecord>(
        'SELECT id, username, email, roles, buttons FROM meiye_users WHERE id = $1',
        [decoded.userId]
      );

      if (result.rows.length === 0) {
        responseHelper.unauthorized(res, 'User not found');
        return;
      }

      // 将用户信息附加到请求对象，保持原有格式
      req.user = result.rows[0]!;
      next();
    } finally {
      client.release();
    }
  } catch {
    // 保持原有的错误响应格式
    res.status(403).json({
      code: '9999',
      data: null,
      message: 'Invalid or expired token'
    });
  }
};
