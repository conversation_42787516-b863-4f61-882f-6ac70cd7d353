import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';

import winston from 'winston';

import type { LogContext } from '../types/index.js';

/**
 * 日志配置
 * 保持与原有日志配置完全一致
 */

// 定义日志级别和颜色，保持原有配置
const logLevels = {
  debug: 4,
  error: 0,
  http: 3,
  info: 2,
  warn: 1
} as const;

const logColors = {
  debug: 'white',
  error: 'red',
  http: 'magenta',
  info: 'green',
  warn: 'yellow'
} as const;

// 添加颜色到winston
winston.addColors(logColors);

// 定义日志格式，保持原有格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
);

// 定义文件日志格式（不包含颜色），保持原有格式
const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
);

// 创建日志目录（如果不存在），保持原有逻辑
const logDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 定义传输器（输出目标），保持原有配置
const transports = [
  // 控制台输出
  new winston.transports.Console({
    format: logFormat,
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug'
  }),

  // 错误日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    format: fileLogFormat,
    level: 'error'
  }),

  // 所有日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'combined.log'),
    format: fileLogFormat
  })
];

// 创建logger实例，保持原有配置
const winstonLogger = winston.createLogger({
  exitOnError: false,
  format: logFormat,
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  levels: logLevels,
  transports
});

/**
 * 日志方法类型定义
 */
interface LogMethod {
  (message: string, meta?: LogContext): void;
}

interface CategoryLogger {
  readonly error: LogMethod;
  readonly info: LogMethod;
  readonly warn: LogMethod;
}

interface Logger {
  readonly api: CategoryLogger;
  readonly auth: CategoryLogger;
  readonly db: CategoryLogger;
  readonly debug: LogMethod;
  readonly error: LogMethod;
  readonly http: LogMethod;
  readonly info: LogMethod;
  readonly logger: winston.Logger;
  readonly warn: LogMethod;
}

/**
 * 导出logger和便捷方法，保持与原有接口完全一致
 */
export const logger: Logger = {
  // API日志
  api: {
    error: (message: string, meta: LogContext = {}) => winstonLogger.error(`[API] ${message}`, meta),
    info: (message: string, meta: LogContext = {}) => winstonLogger.info(`[API] ${message}`, meta),
    warn: (message: string, meta: LogContext = {}) => winstonLogger.warn(`[API] ${message}`, meta)
  },

  // 认证日志
  auth: {
    error: (message: string, meta: LogContext = {}) => winstonLogger.error(`[AUTH] ${message}`, meta),
    info: (message: string, meta: LogContext = {}) => winstonLogger.info(`[AUTH] ${message}`, meta),
    warn: (message: string, meta: LogContext = {}) => winstonLogger.warn(`[AUTH] ${message}`, meta)
  },

  // 数据库日志
  db: {
    error: (message: string, meta: LogContext = {}) => winstonLogger.error(`[DB] ${message}`, meta),
    info: (message: string, meta: LogContext = {}) => winstonLogger.info(`[DB] ${message}`, meta),
    warn: (message: string, meta: LogContext = {}) => winstonLogger.warn(`[DB] ${message}`, meta)
  },

  debug: (message: string, meta: LogContext = {}) => winstonLogger.debug(message, meta),
  // 便捷方法
  error: (message: string, meta: LogContext = {}) => winstonLogger.error(message, meta),
  http: (message: string, meta: LogContext = {}) => winstonLogger.http(message, meta),
  info: (message: string, meta: LogContext = {}) => winstonLogger.info(message, meta),
  logger: winstonLogger,
  warn: (message: string, meta: LogContext = {}) => winstonLogger.warn(message, meta)
};

export default logger;
