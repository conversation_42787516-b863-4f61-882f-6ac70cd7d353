import process from 'node:process';

import { Pool } from 'pg';

import type { DatabaseConfig } from '../types';

import { logger } from './logger';

/**
 * 数据库配置
 * 保持与原有配置完全一致
 */
const createDatabaseConfig = (): DatabaseConfig => {
  const config: DatabaseConfig = {
    connectionTimeoutMillis: 2000,
    database: process.env.DB_NAME || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    idleTimeoutMillis: 30000,
    max: 20,
    password: process.env.DB_PASSWORD || '',
    port: Number(process.env.DB_PORT) || 5432,
    user: process.env.DB_USER || 'postgres'
  };

  return config;
};

/**
 * 创建数据库连接池
 * 保持与原有逻辑完全一致
 */
const createPool = (): Pool => {
  const config = createDatabaseConfig();

  const pool = new Pool({
    connectionTimeoutMillis: config.connectionTimeoutMillis,
    database: config.database,
    host: config.host,
    idleTimeoutMillis: config.idleTimeoutMillis,
    max: config.max,
    password: config.password,
    port: config.port,
    user: config.user
  });

  // 保持原有的事件监听逻辑
  pool.on('connect', () => {
    logger.info('[DB] Connected to PostgreSQL database');
  });

  pool.on('error', (err: Error) => {
    logger.error('[DB] Unexpected error on idle client', { error: err.message });
    process.exit(-1);
  });

  return pool;
};

// 导出连接池实例，保持与原有模块导出方式一致
export const pool = createPool();
export default pool;
