<div align="center">
 <img src="./public/favicon.svg" width="160" />
 <h1>React SoybeanAdmin</h1>
  <span><a href="./README.en-US.md">English</a> | 中文</span>
</div>

---

[![license](https://img.shields.io/badge/license-MIT-green.svg)](./LICENSE)

> [!NOTE]
> If you find `React SoybeanAdmin` helpful or like our project, please give us a ⭐️ on GitHub. Your support motivates us to keep improving and adding new features! Thank you for your support!

## Special Thanks

This project is based on the outstanding open-source project [Soybean Admin](https://github.com/soybeanjs/soybean-admin), developed by [Soybean](https://github.com/honghuangdc). It is a React implementation of the original project. Special thanks to Soybean for their contributions to open source and for providing an excellent solution for admin panel development. If you like this project, please also give a ⭐️ to the original [Soybean Admin](https://github.com/soybeanjs/soybean-admin).

## Introduction

[`React SoybeanAdmin`](https://github.com/mufeng889/react-soybean-admin) is a fresh and elegant admin panel template built on the React19 stack. It adopts the latest frontend technologies, including:

- 🚀 React 19 + ReactRouter V7 + Redux/toolkit
- 🎨 Ant Design + UnoCSS
- 📦 Vite 6 + TypeScript
- 🔐 Role-based access control
- 📱 Mobile-friendly adaptation
- 🌍 Internationalization support
- 🎯 Mock data integration
- 📚 Comprehensive documentation

### Key Features:

- 💡 Strict coding standards and elegant architecture
- ⚡️ Ready to use out of the box, no complex setup needed
- 🛠️ Rich components and theme customization
- 📋 Automated file routing system
- 🔧 Excellent type support
- 📱 Responsive design, perfectly adapted for mobile
- 🎨 Optimistic UI: Automatically captures and displays friendly error messages for debugging and monitoring purposes within components.
- 🚀 Advanced routing features: Extends React-Router V7 API ，Provide convention-based file routing similar to Next.js, while also allowing custom reusable routes..
- ⚡️ CLI tools: Built-in tools for Git commits, file deletions, releases, and more.

Whether you're learning the latest frontend technologies or developing enterprise-grade admin panels, React SoybeanAdmin is your ideal choice.

## Branches

- **`master` branch**: Latest stable version, based on React19 + ReactRouter V7.
- **`v18-router6` branch**: Legacy version, based on React18 + ReactRouter V6.

Both branches will continue to receive bug fixes. If you need the older version, switch to the appropriate branch. The `v18-router6` branch will remain advanced and feature-rich for 3-5 years.

## Versions

### React Version

- **React19 Version:**
  - [Preview](https://github.com/mufeng889/react-soybean-admin/)
  - [Faster Access in China](https://react-soybean-admin.pages.dev/)
  - [GitHub Repository](https://github.com/mufeng889/react-soybean-admin)
  - [Gitee Repository](https://gitee.com/sjgk_dl/react-admin)

#### Documentation

- [Docs](https://react-soybean-docs.ohh-889.com/index-cn?theme=dark)

### Vue Versions

- **NaiveUI Version:**
  - [Preview](https://naive.soybeanjs.cn/) - A fresh and elegant admin panel built with Vue3 + NaiveUI.
  - [GitHub Repository](https://github.com/soybeanjs/soybean-admin) - Get the latest source code and contribute.
  - [Gitee Repository](https://gitee.com/honghuangdc/soybean-admin) - Faster access for China users.
  - Features:
    - Complete TypeScript support
    - Rich theme customization
    - Elegant coding style
    - Detailed documentation
- **AntDesignVue Version:**
  - [Preview](https://antd.soybeanjs.cn/)
  - [GitHub Repository](https://github.com/soybeanjs/soybean-admin-antd)
  - [Gitee Repository](https://gitee.com/honghuangdc/soybean-admin-antd)

- **Legacy Version:**
  - [Preview](https://legacy.soybeanjs.cn/)
  - [GitHub Repository](https://github.com/soybeanjs/soybean-admin/tree/legacy)

## Ant Design-Themed Comprehensive Documentation

- [Docs](https://react-soybean-docs.ohh-889.com/index-cn?theme=dark)
![](https://ohh-**********.cos.ap-nanjing.myqcloud.com/docs-home.jpg)

## Demo Screenshots

![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-01.png)
![](https://ohh-**********.cos.ap-nanjing.myqcloud.com/mobile.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-02.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-03.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-04.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-05.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-06.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-07.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-08.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-09.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-10.png)
![](https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/soybean-admin-v1-mobile.png)

## Usage

### Prerequisites

Ensure your environment meets the following requirements:

- **git**: For cloning and managing the project.
- **NodeJS**: >=18.12.0, recommended 18.19.0 or higher.
- **pnpm**: >= 8.7.0, recommended 8.14.0 or higher.

### Clone the Repository

```bash
git clone https://github.com/soybeanjs/soybean-admin.git
```

### Install Dependencies

```bash
pnpm i
```

> Since the project uses pnpm monorepo management, please do not use npm or yarn to install dependencies.

### Start Development Server

```bash
pnpm dev
```

### Build Project

```bash
pnpm build
```

## Contribution

We warmly welcome and appreciate all forms of contributions. If you have any ideas or suggestions, please share them via [pull requests](https://github.com/mufeng889/react-soybean-admin/pulls) or GitHub [issues](https://github.com/mufeng889/react-soybean-admin/issues/new).

## Git Commit Guidelines

This project includes a `commit` command to generate commit messages following the [Conventional Commits](https://www.conventionalcommits.org/) standard. When submitting PRs, please use the `pnpm commit` command to create standardized commit messages.

## Browser Support

For the best experience, use the latest version of Chrome.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/archive/internet-explorer_9-11/internet-explorer_9-11_48x48.png" alt="IE" width="24px" height="24px"  />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/) |
| --- | --- | --- | --- | --- |
| not support | last 2 versions | last 2 versions | last 2 versions | last 2 versions |

## Open Source Authors

[Ohh-889](https://github.com/mufeng889)

[Soybean](https://github.com/honghuangdc)

## Contributors

Thanks to the following contributors. If you want to contribute, please refer to [Contribution](#contribution).

<a href="https://github.com/mufeng889/react-soybean-admin/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=mufeng889/react-soybean-admin" />
</a>

## Community

`React Soybean` is a completely free and open-source project, aiming to make developing medium-to-large admin systems easier. We also provide QQ and WeChat groups for user discussions. Feel free to ask questions in the group.

  <div>
   <p>QQ Group</p>
    <img src="https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/qq-soybean-admin-4.jpg" style="width:200px" />
  </div>

 <div>
  <p>Join the WeChat group via the QR code below</p>
  <img src="https://soybeanjs-**********.cos.ap-guangzhou.myqcloud.com/uPic/wechat-soybeanjs.jpg" style="width:200px" />
 </div>

 <div>
  <p>Scan below to join the WeChat group</p>
  <img src="
  https://ohh-**********.cos.ap-nanjing.myqcloud.com/ohh-889.jpg" style="width:200px" />
 </div>

## License

This project is licensed under [MIT © 2021 Soybean](./LICENSE). It is intended for learning and reference only. For commercial use, please retain the original author's copyright information. The author does not guarantee or take responsibility for any risks associated with using the software.

